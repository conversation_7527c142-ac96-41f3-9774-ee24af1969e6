import fetch from 'node-fetch';

interface IntercomUser {
  type: 'user';
  id?: string;
  user_id?: string;
  email?: string;
  name?: string;
  phone?: string;
  avatar?: {
    type: 'avatar';
    image_url: string;
  };
  signed_up_at?: number;
  last_seen_at?: number;
  owner_id?: number;
  unsubscribed_from_emails?: boolean;
  custom_attributes?: Record<string, any>;
}

interface IntercomContact {
  type: 'contact';
  id?: string;
  external_id?: string;
  email?: string;
  name?: string;
  phone?: string;
  avatar?: {
    type: 'avatar';
    image_url: string;
  };
  signed_up_at?: number;
  last_seen_at?: number;
  owner_id?: number;
  unsubscribed_from_emails?: boolean;
  custom_attributes?: Record<string, any>;
}

interface IntercomConversation {
  type: 'admin_initiated_conversation' | 'user_initiated_conversation';
  id?: string;
  from: {
    type: 'user' | 'admin' | 'contact';
    id: string;
  };
  body: string;
  message_type?: 'comment' | 'note';
}

class IntercomService {
  private accessToken: string;
  private baseUrl = 'https://api.intercom.io';

  constructor() {
    this.accessToken = process.env.INTERCOM_ACCESS_TOKEN || '';
    if (!this.accessToken) {
      console.warn('Intercom access token not found. Set INTERCOM_ACCESS_TOKEN environment variable.');
    }
  }

  private async makeRequest(endpoint: string, options: any = {}) {
    if (!this.accessToken) {
      throw new Error('Intercom access token not configured');
    }

    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Intercom API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  // User Management
  async createOrUpdateUser(userData: Partial<IntercomUser>) {
    try {
      return await this.makeRequest('/users', {
        method: 'POST',
        body: JSON.stringify(userData),
      });
    } catch (error) {
      console.error('Error creating/updating Intercom user:', error);
      throw error;
    }
  }

  async getUser(userId: string) {
    try {
      return await this.makeRequest(`/users/${userId}`);
    } catch (error) {
      console.error('Error fetching Intercom user:', error);
      throw error;
    }
  }

  async getUserByEmail(email: string) {
    try {
      return await this.makeRequest(`/users?email=${encodeURIComponent(email)}`);
    } catch (error) {
      console.error('Error fetching Intercom user by email:', error);
      throw error;
    }
  }

  async deleteUser(userId: string) {
    try {
      return await this.makeRequest(`/users/${userId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Error deleting Intercom user:', error);
      throw error;
    }
  }

  // Contact Management
  async createOrUpdateContact(contactData: Partial<IntercomContact>) {
    try {
      return await this.makeRequest('/contacts', {
        method: 'POST',
        body: JSON.stringify(contactData),
      });
    } catch (error) {
      console.error('Error creating/updating Intercom contact:', error);
      throw error;
    }
  }

  // Conversation Management
  async createConversation(conversationData: Partial<IntercomConversation>) {
    try {
      return await this.makeRequest('/conversations', {
        method: 'POST',
        body: JSON.stringify(conversationData),
      });
    } catch (error) {
      console.error('Error creating Intercom conversation:', error);
      throw error;
    }
  }

  async getConversations(userId?: string) {
    try {
      const endpoint = userId ? `/conversations?intercom_user_id=${userId}` : '/conversations';
      const response = await this.makeRequest(endpoint);

      // Fetch detailed conversation data for each conversation
      if (response.conversations) {
        const detailedConversations = await Promise.all(
          response.conversations.map(async (conv: any) => {
            try {
              const detailed = await this.getConversationDetails(conv.id);
              return detailed;
            } catch (error) {
              console.error(`Error fetching details for conversation ${conv.id}:`, error);
              return conv; // Return basic conversation if details fail
            }
          })
        );
        response.conversations = detailedConversations;
      }

      return response;
    } catch (error) {
      console.error('Error fetching Intercom conversations:', error);
      throw error;
    }
  }

  async getConversationDetails(conversationId: string) {
    try {
      return await this.makeRequest(`/conversations/${conversationId}`);
    } catch (error) {
      console.error('Error fetching Intercom conversation details:', error);
      throw error;
    }
  }

  async replyToConversation(conversationId: string, message: string, messageType: 'comment' | 'note' = 'comment') {
    try {
      return await this.makeRequest(`/conversations/${conversationId}/reply`, {
        method: 'POST',
        body: JSON.stringify({
          message_type: messageType,
          type: 'admin',
          body: message,
        }),
      });
    } catch (error) {
      console.error('Error replying to Intercom conversation:', error);
      throw error;
    }
  }

  async deleteConversation(conversationId: string) {
    try {
      // Note: Intercom doesn't have a direct delete endpoint for conversations
      // Instead, we'll close the conversation and add a note that it was deleted by admin
      await this.makeRequest(`/conversations/${conversationId}/reply`, {
        method: 'POST',
        body: JSON.stringify({
          message_type: 'note',
          type: 'note',
          body: 'This conversation was deleted by an administrator.',
        }),
      });

      // Close the conversation
      return await this.makeRequest(`/conversations/${conversationId}`, {
        method: 'PUT',
        body: JSON.stringify({
          state: 'closed',
        }),
      });
    } catch (error) {
      console.error('Error deleting Intercom conversation:', error);
      throw error;
    }
  }

  // Events and Analytics
  async trackEvent(userId: string, eventName: string, metadata?: Record<string, any>) {
    try {
      return await this.makeRequest('/events', {
        method: 'POST',
        body: JSON.stringify({
          event_name: eventName,
          user_id: userId,
          metadata: metadata || {},
          created_at: Math.floor(Date.now() / 1000),
        }),
      });
    } catch (error) {
      console.error('Error tracking Intercom event:', error);
      throw error;
    }
  }

  // Sync user from your database to Intercom
  async syncUser(user: any) {
    try {
      const intercomUser: Partial<IntercomUser> = {
        user_id: user.id?.toString(),
        email: user.email,
        name: user.name || user.email,
        signed_up_at: user.createdAt ? Math.floor(new Date(user.createdAt).getTime() / 1000) : Math.floor(Date.now() / 1000),
        custom_attributes: {
          role: user.role,
          plan: 'free', // Customize based on your user model
          app_version: '1.0.0',
          last_login: user.lastLoginAt ? Math.floor(new Date(user.lastLoginAt).getTime() / 1000) : undefined,
        },
      };

      return await this.createOrUpdateUser(intercomUser);
    } catch (error) {
      console.error('Error syncing user to Intercom:', error);
      throw error;
    }
  }

  // Webhook verification
  verifyWebhook(body: string, signature: string, secret: string): boolean {
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha1', secret)
      .update(body)
      .digest('hex');

    return signature === `sha1=${expectedSignature}`;
  }
}

export const intercomService = new IntercomService();
