import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import { db } from "./db.js";
import { authMiddleware } from "./middleware/auth.js";
import authRouter from "./routes/auth.js";
import organizerRouter from "./routes/organizer.js";
import contributorRouter from "./routes/contributor.js";
import uploadRouter from "./routes/upload.js";
import dataRequestRouter from './routes/api/send-data-request.js';
import adminRouter from './routes/admin.js';
import commentsRouter from './routes/api/comments.js';
import notificationsRouter, { cleanupOldNotifications } from './routes/notifications.js';
import recipesRouter from './routes/recipes.js';
import bookCustomizationRouter from './routes/bookCustomization.js';
import blurbRouter from './routes/blurb.js';
import pdfRouter from './routes/pdf.js';
import supportRouter from './routes/support.js';
import faqRouter from './routes/faq.js';
import { sendContributorReminders } from './services/reminders.js';
import cron from 'node-cron';
import { projectContributors } from './schema.js';
import { sql } from 'drizzle-orm';

dotenv.config();

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5000',
    'http://localhost:8081', // Mobile app (Expo web)
    'https://storyworth.vercel.app',
    'https://storyworth.onrender.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Serve uploaded files statically
app.use('/uploads', express.static('uploads'));

// Routes
app.use("/api/auth", authRouter);
app.use("/api/organizer", organizerRouter);
app.use("/api/contributor", contributorRouter);
app.use("/api/upload", uploadRouter);
app.use('/api', dataRequestRouter);
app.use('/api/admin', adminRouter);
app.use('/api/comments', commentsRouter);
app.use('/api/notifications', notificationsRouter);
app.use('/api/recipes', recipesRouter);
app.use('/api/book-customization', bookCustomizationRouter);
app.use('/api/blurb', blurbRouter);
app.use('/api/pdf', pdfRouter);
app.use('/api/support', supportRouter);
app.use('/api/faq', faqRouter);

// Root route handler
app.get('/', (_req, res) => {
  res.json({
    message: 'Storyworth API Server',
    docs: 'All endpoints are under /api/*',
    health: '/api/health'
  });
});

// Health check endpoint
app.get('/api/health', (_req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});

// Helper function to get cron expression for a reminder frequency
function getCronExpression(frequency: string): string {
  switch (frequency) {
    case '1min': return '* * * * *'; // Every minute
    case '2min': return '*/2 * * * *'; // Every 2 minutes
    case '5min': return '*/5 * * * *'; // Every 5 minutes
    case '15min': return '*/15 * * * *'; // Every 15 minutes
    case '30min': return '*/30 * * * *'; // Every 30 minutes
    case '1hour': return '0 * * * *'; // Every hour
    case 'daily': return '0 9 * * *'; // Every day at 9 AM
    case 'weekly': return '0 9 * * 1'; // Every Monday at 9 AM
    case 'biweekly': return '0 9 1,15 * *'; // 1st and 15th at 9 AM
    case 'monthly': return '0 9 1 * *'; // 1st of every month at 9 AM
    default: return '0 9 * * *'; // Default to daily at 9 AM
  }
}

// Function to get the most frequent reminder interval from active contributors
async function getMostFrequentReminderInterval(): Promise<string> {
  console.log('Checking for most frequent reminder interval...');
  const result = await db.select({
    frequency: projectContributors.reminderFrequency
  })
  .from(projectContributors)
  .where(sql`${projectContributors.status} IN ('pending', 'accepted')`)
  .orderBy(sql`CASE
    WHEN ${projectContributors.reminderFrequency} = '1min' THEN 1
    WHEN ${projectContributors.reminderFrequency} = '2min' THEN 2
    WHEN ${projectContributors.reminderFrequency} = '5min' THEN 3
    WHEN ${projectContributors.reminderFrequency} = '15min' THEN 4
    WHEN ${projectContributors.reminderFrequency} = '30min' THEN 5
    WHEN ${projectContributors.reminderFrequency} = '1hour' THEN 6
    WHEN ${projectContributors.reminderFrequency} = 'daily' THEN 7
    WHEN ${projectContributors.reminderFrequency} = 'weekly' THEN 8
    WHEN ${projectContributors.reminderFrequency} = 'biweekly' THEN 9
    WHEN ${projectContributors.reminderFrequency} = 'monthly' THEN 10
    ELSE 11
  END`)
  .limit(1);

  const frequency = result[0]?.frequency || 'daily';
  console.log(`Most frequent reminder interval found: ${frequency}`);
  return frequency;
}

let currentReminderCronJob: cron.ScheduledTask | null = null;

// Function to update the reminder cron schedule
async function updateReminderCronSchedule() {
  console.log('Updating reminder cron schedule...');
  const frequency = await getMostFrequentReminderInterval();
  const cronExpression = getCronExpression(frequency);

  // Stop the current cron job if it exists
  if (currentReminderCronJob) {
    console.log('Stopping existing reminder cron job...');
    currentReminderCronJob.stop();
  }

  // Start a new cron job with the updated schedule
  console.log(`Starting new reminder cron job with expression: ${cronExpression}`);
  currentReminderCronJob = cron.schedule(cronExpression, async () => {
    console.log(`[${new Date().toISOString()}] Running contributor reminders cron job (frequency: ${frequency})...`);
    try {
      await sendContributorReminders();
      console.log(`[${new Date().toISOString()}] Contributor reminders sent successfully`);
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error running contributor reminders:`, error);
    }
  });

  console.log(`Updated reminder schedule to run ${frequency}`);
}

// Schedule notification cleanup to run every minute for testing
cron.schedule('* * * * *', async () => {
  console.log('Running notification cleanup...');
  await cleanupOldNotifications();
});

// Schedule daily cleanup of old notifications
cron.schedule('0 0 * * *', async () => {
  console.log('Running daily cleanup of old notifications...');
  await cleanupOldNotifications();
});

// Initialize reminder cron job
console.log('Initializing reminder cron job...');
updateReminderCronSchedule();

// Update reminder schedule every hour to check for changes in reminder frequencies
console.log('Setting up hourly reminder schedule check...');
cron.schedule('0 * * * *', () => {
  console.log('Checking for reminder frequency changes...');
  updateReminderCronSchedule();
});

app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
});
