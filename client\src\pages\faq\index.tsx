import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useToast } from "@/hooks/use-toast";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { 
  Search, 
  ChevronDown, 
  ChevronUp, 
  ThumbsUp, 
  ThumbsDown, 
  BookOpen, 
  ChefHat, 
  Book, 
  Printer, 
  CreditCard, 
  Settings,
  HelpCircle,
  MessageSquare
} from "lucide-react";
import { API_URL } from "@/lib/constants";
import { Link } from "react-router-dom";

interface FaqCategory {
  id: number;
  name: string;
  description: string;
  slug: string;
  icon: string;
  sortOrder: number;
  items: FaqItem[];
}

interface FaqItem {
  id: number;
  question: string;
  answer: string;
  slug: string;
  tags: string[];
  viewCount: number;
  isHelpful: number;
  isNotHelpful: number;
}

const iconMap: Record<string, any> = {
  BookOpen,
  ChefHat,
  Book,
  Printer,
  CreditCard,
  Settings,
  HelpCircle
};

export default function FaqPage() {
  const [categories, setCategories] = useState<FaqCategory[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());
  const [feedbackGiven, setFeedbackGiven] = useState<Set<number>>(new Set());
  const { toast } = useToast();

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (searchQuery.trim()) {
      searchFaqs();
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  }, [searchQuery]);

  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_URL}/faq/categories`);
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      } else {
        throw new Error('Failed to fetch FAQ categories');
      }
    } catch (error) {
      console.error('Error fetching FAQ categories:', error);
      toast({
        title: "Error",
        description: "Failed to load FAQ categories",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const searchFaqs = async () => {
    try {
      setIsSearching(true);
      const response = await fetch(`${API_URL}/faq/search?q=${encodeURIComponent(searchQuery)}&limit=20`);
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data);
      } else {
        throw new Error('Failed to search FAQs');
      }
    } catch (error) {
      console.error('Error searching FAQs:', error);
      toast({
        title: "Error",
        description: "Failed to search FAQs",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  const toggleItem = (itemId: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(itemId)) {
      newOpenItems.delete(itemId);
    } else {
      newOpenItems.add(itemId);
    }
    setOpenItems(newOpenItems);
  };

  const giveFeedback = async (itemId: number, helpful: boolean) => {
    if (feedbackGiven.has(itemId)) {
      toast({
        title: "Feedback Already Given",
        description: "You have already provided feedback for this FAQ item.",
      });
      return;
    }

    try {
      const response = await fetch(`${API_URL}/faq/items/${itemId}/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ helpful }),
      });

      if (response.ok) {
        setFeedbackGiven(prev => new Set([...prev, itemId]));
        toast({
          title: "Thank You!",
          description: "Your feedback has been recorded.",
        });
      } else {
        throw new Error('Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast({
        title: "Error",
        description: "Failed to submit feedback",
        variant: "destructive",
      });
    }
  };

  const renderFaqItem = (item: FaqItem, categoryName?: string) => {
    const isOpen = openItems.has(item.id);
    const hasFeedback = feedbackGiven.has(item.id);

    return (
      <Card key={item.id} className="mb-4">
        <Collapsible open={isOpen} onOpenChange={() => toggleItem(item.id)}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <CardTitle className="text-left text-lg font-medium">
                    {item.question}
                  </CardTitle>
                  {categoryName && (
                    <Badge variant="secondary" className="mt-2">
                      {categoryName}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    {item.viewCount} views
                  </span>
                  {isOpen ? (
                    <ChevronUp className="h-5 w-5" />
                  ) : (
                    <ChevronDown className="h-5 w-5" />
                  )}
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0">
              <div className="prose prose-sm max-w-none">
                <div dangerouslySetInnerHTML={{ __html: item.answer.replace(/\n/g, '<br>') }} />
              </div>
              
              {item.tags && item.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-4">
                  {item.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              <div className="flex items-center justify-between mt-6 pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  Was this helpful?
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => giveFeedback(item.id, true)}
                    disabled={hasFeedback}
                    className="flex items-center gap-1"
                  >
                    <ThumbsUp className="h-4 w-4" />
                    Yes ({item.isHelpful})
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => giveFeedback(item.id, false)}
                    disabled={hasFeedback}
                    className="flex items-center gap-1"
                  >
                    <ThumbsDown className="h-4 w-4" />
                    No ({item.isNotHelpful})
                  </Button>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading FAQ...</p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Frequently Asked Questions</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Find answers to common questions about RecipeBook. Can't find what you're looking for? 
            <Link to="/support" className="text-primary hover:underline ml-1">
              Contact our support team
            </Link>
          </p>
        </div>

        {/* Search */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search FAQ..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Search Results */}
        {searchQuery.trim() && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">
              Search Results {isSearching && <span className="text-sm text-muted-foreground">(searching...)</span>}
            </h2>
            {searchResults.length > 0 ? (
              <div>
                {searchResults.map((item) => renderFaqItem(item, item.categoryName))}
              </div>
            ) : !isSearching ? (
              <Card>
                <CardContent className="text-center py-8">
                  <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No results found for "{searchQuery}"</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Try different keywords or browse categories below
                  </p>
                </CardContent>
              </Card>
            ) : null}
          </div>
        )}

        {/* Categories */}
        {!searchQuery.trim() && (
          <div>
            <h2 className="text-xl font-semibold mb-6">Browse by Category</h2>
            <div className="space-y-6">
              {categories.map((category) => {
                const IconComponent = iconMap[category.icon] || HelpCircle;
                
                return (
                  <Card key={category.id}>
                    <CardHeader>
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <IconComponent className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-xl">{category.name}</CardTitle>
                          <p className="text-muted-foreground">{category.description}</p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {category.items.map((item) => renderFaqItem(item))}
                        {category.items.length === 0 && (
                          <p className="text-muted-foreground text-center py-4">
                            No FAQ items in this category yet.
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        )}

        {/* Still need help */}
        <Card className="mt-12">
          <CardContent className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-primary mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Still need help?</h3>
            <p className="text-muted-foreground mb-4">
              Can't find the answer you're looking for? Our support team is here to help.
            </p>
            <Link to="/support">
              <Button>Contact Support</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </ProtectedRoute>
  );
}
