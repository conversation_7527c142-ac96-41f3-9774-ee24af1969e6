import { sql } from 'drizzle-orm';
import { pgTable, text, timestamp, integer, serial, json } from 'drizzle-orm/pg-core';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as dotenv from 'dotenv';

dotenv.config();

console.log('Starting migration: Creating print_orders table for Blurb API integration');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

const db = drizzle(pool);

// Define the print_orders table schema - not using references in the schema definition
// since we'll use direct SQL for the actual table creation
const printOrders = pgTable('print_orders', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull(),
  blurbOrderId: text('blurb_order_id').notNull(),
  status: text('status').notNull(),
  bookData: json('book_data').notNull(),
  shippingAddress: json('shipping_address').notNull(),
  trackingUrl: text('tracking_url'),
  shippingInfo: json('shipping_info'),
  estimatedDelivery: timestamp('estimated_delivery'),
  totalCost: text('total_cost'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

async function main() {
  console.log('Connected to database, beginning print_orders table migration');
  
  try {
    // Check if table already exists
    console.log('Checking if print_orders table already exists...');
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'print_orders'
      );
    `);
    
    if (tableExists.rows[0]?.exists) {
      console.log('print_orders table already exists, skipping creation');
      return;
    }
    
    console.log('print_orders table does not exist, creating...');
    
    // Create the print_orders table
    await db.execute(sql`
      CREATE TABLE print_orders (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
        blurb_order_id TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        book_data JSONB NOT NULL,
        shipping_address JSONB NOT NULL,
        tracking_url TEXT,
        shipping_info JSONB,
        estimated_delivery TIMESTAMP WITHOUT TIME ZONE,
        total_cost TEXT,
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
      );
    `);
    
    console.log('Successfully created print_orders table');

    // Create indexes for better query performance
    console.log('Creating indexes for print_orders table...');
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS print_orders_user_id_idx ON print_orders(user_id);
    `);
    
    console.log('Successfully created index on user_id');
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS print_orders_blurb_order_id_idx ON print_orders(blurb_order_id);
    `);
    
    console.log('Successfully created index on blurb_order_id');
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS print_orders_status_idx ON print_orders(status);
    `);
    
    console.log('Successfully created index on status');
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS print_orders_created_at_idx ON print_orders(created_at);
    `);
    
    console.log('Successfully created index on created_at');
    
    // Add unique constraint on blurb_order_id to prevent duplicates
    await db.execute(sql`
      ALTER TABLE print_orders ADD CONSTRAINT print_orders_blurb_order_id_unique UNIQUE (blurb_order_id);
    `);
    
    console.log('Successfully added unique constraint on blurb_order_id');
    
    // Verify table creation
    console.log('Verifying table structure...');
    const tableInfo = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'print_orders'
      ORDER BY ordinal_position;
    `);
    
    console.log('print_orders table structure:');
    tableInfo.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable}, default: ${row.column_default})`);
    });
    
    // Verify indexes
    console.log('Verifying indexes...');
    const indexInfo = await db.execute(sql`
      SELECT indexname, indexdef
      FROM pg_indexes
      WHERE tablename = 'print_orders';
    `);
    
    console.log('print_orders table indexes:');
    indexInfo.rows.forEach(row => {
      console.log(`  - ${row.indexname}: ${row.indexdef}`);
    });
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    // End the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

main().catch((error) => {
  console.error('Migration failed:', error);
  process.exit(1);
});
