import React, { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/use-auth';

// Extend the Window interface to include Intercom
declare global {
  interface Window {
    Intercom: any;
    intercomSettings: any;
  }
}

interface IntercomChatProps {
  appId?: string;
}

export function IntercomChat({ appId }: IntercomChatProps) {
  const { user } = useAuth();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Get App ID from environment or props
    const intercomAppId = appId || import.meta.env.VITE_INTERCOM_APP_ID;

    if (!intercomAppId) {
      console.warn('Intercom App ID not found. Please set VITE_INTERCOM_APP_ID in your environment variables.');
      return;
    }

    // Configure Intercom settings
    const intercomSettings = {
      app_id: intercomAppId,
      // Always include basic settings
      hide_default_launcher: false,
      // If user is logged in, identify them
      ...(user && {
        name: user.name || user.email || 'RecipeBook User',
        email: user.email,
        user_id: user.id?.toString(),
        created_at: user.createdAt ? Math.floor(new Date(user.createdAt).getTime() / 1000) : Math.floor(Date.now() / 1000),
        // Add custom attributes
        custom_attributes: {
          role: user.role || 'user',
          plan: 'free',
          app_version: '1.0.0',
          environment: process.env.NODE_ENV || 'development'
        }
      })
    };

    window.intercomSettings = intercomSettings;

    // Load Intercom script if not already loaded
    if (!window.Intercom) {
      // Create the Intercom function
      window.Intercom = function(...args: any[]) {
        (window.Intercom as any).q = (window.Intercom as any).q || [];
        (window.Intercom as any).q.push(args);
      };

      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.async = true;
      script.src = `https://widget.intercom.io/widget/${intercomAppId}`;

      script.onload = () => {
        setIsLoaded(true);
      };

      document.head.appendChild(script);
    }

    // Boot or update Intercom
    try {
      window.Intercom('boot', window.intercomSettings);
    } catch (error) {
      console.error('Error booting Intercom:', error);
    }

    // Cleanup function
    return () => {
      if (window.Intercom) {
        window.Intercom('shutdown');
      }
    };
  }, [user, appId]);

  // Update Intercom when user changes
  useEffect(() => {
    if (window.Intercom && user) {
      window.Intercom('update', {
        name: user.name || user.email,
        email: user.email,
        user_id: user.id?.toString(),
        custom_attributes: {
          role: user.role,
          plan: 'free'
        }
      });
    }
  }, [user]);

  // This component doesn't render anything visible
  // The Intercom widget is injected by their script
  return null;
}

// Hook to check if Intercom should be enabled
export function useIntercomChat() {
  const [isEnabled, setIsEnabled] = useState(false);

  useEffect(() => {
    // Check if Intercom is configured - only need App ID for frontend widget
    const appId = import.meta.env.VITE_INTERCOM_APP_ID;
    setIsEnabled(!!appId);
  }, []);

  return isEnabled;
}

// Utility functions for Intercom API
export const IntercomAPI = {
  // Show the messenger
  show: () => {
    if (window.Intercom) {
      window.Intercom('show');
    }
  },

  // Hide the messenger
  hide: () => {
    if (window.Intercom) {
      window.Intercom('hide');
    }
  },

  // Show a specific message
  showNewMessage: (message?: string) => {
    if (window.Intercom) {
      window.Intercom('showNewMessage', message);
    }
  },

  // Track an event
  trackEvent: (eventName: string, metadata?: Record<string, any>) => {
    if (window.Intercom) {
      window.Intercom('trackEvent', eventName, metadata);
    }
  },

  // Update user information
  updateUser: (userData: Record<string, any>) => {
    if (window.Intercom) {
      window.Intercom('update', userData);
    }
  },

  // Get unread conversation count
  getUnreadCount: () => {
    if (window.Intercom) {
      return window.Intercom('getUnreadCount');
    }
    return 0;
  }
};
