import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";
import { Crown, Shield, UserCircle2, Search, HelpCircle, MessageSquare } from "lucide-react";
import { Link } from "wouter";
import { API_URL } from '@/lib/constants';
import { Input } from "@/components/ui/input";

interface Contributor {
  id: number;
  name: string;
  email: string;
  status: string;
}

interface RecipeBook {
  id: number;
  name: string;
  description: string;
  status: string;
  role: string;
  createdAt: string;
  contributors: Contributor[];
  organizer?: { name: string };
  organizerId: number;
}

interface RecipeBooksResponse {
  projects: RecipeBook[];
}

export default function RecipeBooks() {
  const [, setLocation] = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'all' | 'my'>('all');
  const [searchQuery, setSearchQuery] = useState('');

  const { data: recipeBooks, isLoading, error } = useQuery<RecipeBooksResponse, Error>({
    queryKey: ["recipeBooks", user?.role, viewMode],
    queryFn: async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Use different endpoints based on user role and view mode
      let endpoint = '';
      if (user?.role === 'organizer') {
        endpoint = viewMode === 'all'
          ? `${API_URL}/organizer/all-projects`
          : `${API_URL}/organizer/my-projects`;
      } else if (user?.role === 'contributor') {
        endpoint = `${API_URL}/contributor/all-projects`;
      } else if (user?.role === 'admin') {
        endpoint = viewMode === 'all'
          ? `${API_URL}/admin/projects`
          : `${API_URL}/organizer/my-projects`;
      } else {
        throw new Error('User role not supported');
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to fetch recipe books");
      }

      const data = await response.json();
      return { projects: data.projects || [] };
    },
    enabled: !!user?.role
  });

  useEffect(() => {
    if (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    }
  }, [error, toast]);

  // Log when recipe books data changes
  useEffect(() => {
    if (recipeBooks?.projects) {
      console.log('Current recipe books in state:', recipeBooks.projects.length);
    }
  }, [recipeBooks]);

  // Add filtering logic
  const filteredRecipeBooks = recipeBooks?.projects.filter((book) => {
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();
    return (
      book.name.toLowerCase().includes(searchLower) ||
      book.description.toLowerCase().includes(searchLower) ||
      (book.contributors?.some(contributor =>
        contributor?.name?.toLowerCase().includes(searchLower)
      ) ?? false)
    );
  });

  if (!user?.role) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center py-8">Loading user information...</div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Recipe Books</h1>
          <div className="flex items-center gap-4">
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'all' ? 'default' : 'outline'}
                  onClick={() => setViewMode('all')}
                >
                  View All Recipe Books
                </Button>
                <Button
                  variant={viewMode === 'my' ? 'default' : 'outline'}
                  onClick={() => setViewMode('my')}
                >
                  My Recipe Books
                </Button>
              </div>
            )}
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <Button onClick={() => setLocation("/recipe-books/create")}>
                Create New Recipe Book
              </Button>
            )}
          </div>
        </div>
        <div className="text-center py-8">Loading recipe books...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Recipe Books</h1>
          <div className="flex items-center gap-4">
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'all' ? 'default' : 'outline'}
                  onClick={() => setViewMode('all')}
                >
                  View All Recipe Books
                </Button>
                <Button
                  variant={viewMode === 'my' ? 'default' : 'outline'}
                  onClick={() => setViewMode('my')}
                >
                  My Recipe Books
                </Button>
              </div>
            )}
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <Button onClick={() => setLocation("/recipe-books/create")}>
                Create New Recipe Book
              </Button>
            )}
          </div>
        </div>
        <div className="text-center py-8 text-red-500">
          Failed to load recipe books. Please try again later.
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Recipe Books</h1>
        <div className="flex items-center gap-4">
          {(user?.role === 'organizer' || user?.role === 'admin') && (
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'all' ? 'default' : 'outline'}
                onClick={() => setViewMode('all')}
              >
                View All Recipe Books
              </Button>
              <Button
                variant={viewMode === 'my' ? 'default' : 'outline'}
                onClick={() => setViewMode('my')}
              >
                My Recipe Books
              </Button>
            </div>
          )}
          {(user?.role === 'organizer' || user?.role === 'admin') && (
            <Button onClick={() => setLocation("/recipe-books/create")}>
              Create New Recipe Book
            </Button>
          )}
        </div>
      </div>

      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Search boooks by name"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {recipeBooks?.projects && filteredRecipeBooks && filteredRecipeBooks.length > 0 ? (
        <div className="space-y-4">
          <div className="flex flex-col gap-4">
            {filteredRecipeBooks.map((book: RecipeBook) => {
              console.log('Rendering book:', book.id, book.name);
              return (
                <Card key={book.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <CardTitle>{book.name}</CardTitle>
                      <CardDescription>
                        Created on {new Date(book.createdAt).toLocaleDateString()}
                      </CardDescription>
                    </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{book.description}</p>
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-gray-500">
                        Contributors: {book.contributors?.length || 0}
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => setLocation(`/recipe-books/${book.id}`)}
                      >
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
          <div className="text-center text-sm text-muted-foreground">
            Showing {filteredRecipeBooks.length} of {recipeBooks.projects.length} recipe books
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          {searchQuery ? 'No recipe books found matching your search.' : 'No recipe books found. Create your first recipe book!'}
        </div>
      )}

      {/* Help Section */}
      <div className="mt-12 pt-8 border-t">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-primary" />
                Need Help?
              </CardTitle>
              <CardDescription>
                Find answers to common questions about creating and managing recipe books
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-sm">
                  <p className="font-medium mb-1">Popular topics:</p>
                  <ul className="text-muted-foreground space-y-1">
                    <li>• How to create your first recipe book</li>
                    <li>• Inviting family members to contribute</li>
                    <li>• Organizing recipes into chapters</li>
                    <li>• Customizing your book's appearance</li>
                  </ul>
                </div>
                <Link href="/faq">
                  <Button variant="outline" className="w-full">
                    <HelpCircle className="h-4 w-4 mr-2" />
                    Browse FAQ
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-primary" />
                Still Need Support?
              </CardTitle>
              <CardDescription>
                Can't find what you're looking for? Our support team is here to help
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-sm">
                  <p className="font-medium mb-1">Get help with:</p>
                  <ul className="text-muted-foreground space-y-1">
                    <li>• Technical issues</li>
                    <li>• Account problems</li>
                    <li>• Billing questions</li>
                    <li>• Feature requests</li>
                  </ul>
                </div>
                <Link href="/support">
                  <Button variant="outline" className="w-full">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Contact Support
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}