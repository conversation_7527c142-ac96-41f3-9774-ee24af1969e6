import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { QueryClientProvider } from '@tanstack/react-query';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { AuthProvider } from './src/hooks/use-auth';
import { queryClient } from './src/lib/queryClient';
import { Router, Route, Switch, useLocation } from './src/lib/router';
import { Colors } from './src/lib/constants';

// Import all pages exactly like web client
import NotFound from './src/pages/not-found';
import AuthForm from './src/components/auth/auth-form';
import { ProtectedRoute } from './src/components/auth/protected-route';
import { UserRole } from './src/lib/constants';
import RecipeBooks from './src/pages/recipe-books';
import CreateRecipeBook from './src/pages/recipe-books/create';
import CreateRecipe from './src/pages/recipes/create';
import AdminPanel from './src/pages/admin';
import AdminDashboard from './src/pages/admin/dashboard';
import OrganizerDashboard from './src/pages/organizer';
import AcceptInvite from './src/pages/accept-invite';
import ContributorDashboard from './src/pages/contributor/dashboard';
import ProjectDetails from './src/pages/recipe-books/[id]';
import EditRecipe from './src/pages/recipes/[id]/edit';
import RecipeApprovals from './src/pages/organizer/recipe-approvals';
import Settings from './src/pages/settings';
import { PrivacyPolicyPage } from './src/pages/privacy-policy';
import { DataRequestPage } from './src/pages/data-request';
import { TermsPage } from './src/pages/terms';
import { CookiePreferencesPage } from './src/pages/cookie-preferences';
import { PricingCalculatorPage } from './src/pages/pricing-calculator';
import OrderTracking from './src/pages/order-tracking';
import ErrorBoundary from './src/components/error-boundary';
import { Header } from './src/components/layout/header';
import { Footer } from './src/components/layout/footer';
import { CookieConsent } from './src/components/privacy/cookie-consent';
import { Toaster } from './src/components/ui/toaster';
import { TooltipProvider } from './src/components/ui/tooltip';

// Simple redirect component
const Redirect = ({ to }: { to: string }) => {
  const [, setLocation] = useLocation();

  useEffect(() => {
    setLocation(to);
  }, [to, setLocation]);

  return null;
};

function AppContent() {
  const [location] = useLocation();

  useEffect(() => {
    console.log('Current location:', location);
  }, [location]);

  return (
    <>
      <Toaster />
      <Header />
      <Switch>
                    <Route path="/" component={() => <Redirect to="/recipe-books" />} />
                    <Route path="/login" component={AuthForm} />
                    <Route path="/accept-invite" component={AcceptInvite} />

                    {/* Protected Routes */}
                    <Route path="/recipe-books">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <RecipeBooks />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/recipe-books/create">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <CreateRecipeBook />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/recipe-books/:id">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <ProjectDetails />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/recipes/create">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <CreateRecipe />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/recipes/:id/edit">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <EditRecipe />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/order-tracking">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <OrderTracking />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/admin">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
                        <AdminPanel />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/admin/dashboard">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER]}>
                        <AdminDashboard />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/organizer">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER]}>
                        <OrganizerDashboard />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/contributor/dashboard">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <ContributorDashboard />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/organizer/recipe-approvals">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER]}>
                        <RecipeApprovals />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/settings">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <Settings />
                      </ProtectedRoute>
                    </Route>

                    {/* Pricing Calculator */}
                    <Route path="/pricing-calculator" component={PricingCalculatorPage} />

                    {/* Privacy-related routes */}
                    <Route path="/privacy-policy" component={PrivacyPolicyPage} />
                    <Route path="/data-request" component={DataRequestPage} />
                    <Route path="/terms" component={TermsPage} />
                    <Route path="/cookie-preferences" component={CookiePreferencesPage} />

                    <Route path="*" component={NotFound} />
      </Switch>
      <Footer />
      <CookieConsent />
    </>
  );
}

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ErrorBoundary>
          <QueryClientProvider client={queryClient}>
            <TooltipProvider>
              <Router>
                <AuthProvider>
                  <AppContent />
                </AuthProvider>
              </Router>
              <StatusBar style="dark" backgroundColor={Colors.background} />
            </TooltipProvider>
          </QueryClientProvider>
        </ErrorBoundary>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
