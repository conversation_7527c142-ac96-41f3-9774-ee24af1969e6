import { db } from '../db.js';
import { faqCategories, faqItems } from '../schema.js';

async function addSampleFaqData() {
  console.log('🚀 Starting sample FAQ data migration...');

  try {
    // Insert sample FAQ items for each category
    console.log('📝 Adding sample FAQ items...');

    // Getting Started FAQs
    const gettingStartedItems = [
      {
        categoryId: 1,
        question: "How do I create my first recipe book?",
        answer: "To create your first recipe book, click on 'Create Book' in the navigation menu. You'll be guided through the process of setting up your book, choosing a theme, and inviting contributors.",
        slug: "create-first-recipe-book",
        tags: ["getting-started", "create", "book"],
        sortOrder: 1
      },
      {
        categoryId: 1,
        question: "What is the difference between an organizer and a contributor?",
        answer: "An organizer creates and manages recipe books, invites contributors, and has full control over the book's settings. Contributors can submit recipes to books they've been invited to join.",
        slug: "organizer-vs-contributor",
        tags: ["roles", "organizer", "contributor"],
        sortOrder: 2
      },
      {
        categoryId: 1,
        question: "How do I invite family members to contribute recipes?",
        answer: "In your recipe book dashboard, click 'Invite Contributors' and enter their email addresses. They'll receive an invitation email with instructions to join your book.",
        slug: "invite-family-members",
        tags: ["invite", "family", "contributors"],
        sortOrder: 3
      }
    ];

    // Recipe Submission FAQs
    const recipeSubmissionItems = [
      {
        categoryId: 2,
        question: "What information should I include in my recipe?",
        answer: "Include the recipe title, ingredients with measurements, step-by-step instructions, cooking time, and any special notes or family stories about the recipe.",
        slug: "recipe-information-required",
        tags: ["recipe", "ingredients", "instructions"],
        sortOrder: 1
      },
      {
        categoryId: 2,
        question: "Can I upload photos with my recipes?",
        answer: "Yes! You can upload up to 3 photos per recipe. We recommend including photos of the finished dish and any preparation steps.",
        slug: "upload-recipe-photos",
        tags: ["photos", "images", "upload"],
        sortOrder: 2
      },
      {
        categoryId: 2,
        question: "How do I edit a recipe after submitting it?",
        answer: "You can edit your own recipes by going to 'My Recipes' and clicking the edit button. Organizers can also edit any recipe in their books.",
        slug: "edit-submitted-recipe",
        tags: ["edit", "modify", "update"],
        sortOrder: 3
      }
    ];

    // Book Creation FAQs
    const bookCreationItems = [
      {
        categoryId: 3,
        question: "What themes are available for my recipe book?",
        answer: "We offer several beautiful themes including Classic, Modern, Rustic, and Elegant. Each theme has different color schemes and typography options.",
        slug: "available-book-themes",
        tags: ["themes", "design", "customization"],
        sortOrder: 1
      },
      {
        categoryId: 3,
        question: "Can I customize the cover of my recipe book?",
        answer: "Yes! You can add a custom title, subtitle, and upload your own cover image. You can also choose from our pre-designed cover templates.",
        slug: "customize-book-cover",
        tags: ["cover", "customization", "design"],
        sortOrder: 2
      },
      {
        categoryId: 3,
        question: "How do I organize recipes into chapters?",
        answer: "Recipes are automatically organized by category (appetizers, main dishes, desserts, etc.). You can also create custom chapters and arrange recipes manually.",
        slug: "organize-recipe-chapters",
        tags: ["chapters", "organization", "categories"],
        sortOrder: 3
      }
    ];

    // Printing & Orders FAQs
    const printingOrdersItems = [
      {
        categoryId: 4,
        question: "What printing options are available?",
        answer: "We offer high-quality hardcover and softcover books in various sizes. All books are printed on premium paper with professional binding.",
        slug: "printing-options-available",
        tags: ["printing", "hardcover", "softcover"],
        sortOrder: 1
      },
      {
        categoryId: 4,
        question: "How long does it take to receive my printed book?",
        answer: "Standard printing takes 7-10 business days, plus shipping time. Rush orders are available for an additional fee and take 3-5 business days.",
        slug: "printing-delivery-time",
        tags: ["delivery", "shipping", "timeline"],
        sortOrder: 2
      },
      {
        categoryId: 4,
        question: "Can I order multiple copies of the same book?",
        answer: "Yes! You can order as many copies as you need. We offer bulk discounts for orders of 10 or more copies.",
        slug: "order-multiple-copies",
        tags: ["multiple", "copies", "bulk"],
        sortOrder: 3
      }
    ];

    // Account & Billing FAQs
    const accountBillingItems = [
      {
        categoryId: 5,
        question: "How much does it cost to create a recipe book?",
        answer: "Creating and organizing your recipe book online is free. You only pay when you're ready to print physical copies. Pricing varies by book size and quantity.",
        slug: "recipe-book-cost",
        tags: ["cost", "pricing", "billing"],
        sortOrder: 1
      },
      {
        categoryId: 5,
        question: "What payment methods do you accept?",
        answer: "We accept all major credit cards (Visa, MasterCard, American Express) and PayPal. All payments are processed securely.",
        slug: "accepted-payment-methods",
        tags: ["payment", "credit-card", "paypal"],
        sortOrder: 2
      },
      {
        categoryId: 5,
        question: "Can I cancel my order?",
        answer: "Orders can be cancelled within 24 hours of placement. Once printing has begun, orders cannot be cancelled, but we'll work with you to resolve any issues.",
        slug: "cancel-order-policy",
        tags: ["cancel", "refund", "policy"],
        sortOrder: 3
      }
    ];

    // Technical Support FAQs
    const technicalSupportItems = [
      {
        categoryId: 6,
        question: "I'm having trouble uploading photos. What should I do?",
        answer: "Make sure your photos are in JPG or PNG format and under 10MB each. Try refreshing the page and uploading again. If problems persist, contact our support team.",
        slug: "photo-upload-issues",
        tags: ["photos", "upload", "technical"],
        sortOrder: 1
      },
      {
        categoryId: 6,
        question: "The website is running slowly. How can I fix this?",
        answer: "Try clearing your browser cache and cookies, or try using a different browser. Make sure you have a stable internet connection.",
        slug: "website-running-slowly",
        tags: ["performance", "browser", "cache"],
        sortOrder: 2
      },
      {
        categoryId: 6,
        question: "I forgot my password. How do I reset it?",
        answer: "Click 'Forgot Password' on the login page and enter your email address. You'll receive a password reset link within a few minutes.",
        slug: "reset-forgotten-password",
        tags: ["password", "reset", "login"],
        sortOrder: 3
      }
    ];

    // Insert all FAQ items
    const allItems = [
      ...gettingStartedItems,
      ...recipeSubmissionItems,
      ...bookCreationItems,
      ...printingOrdersItems,
      ...accountBillingItems,
      ...technicalSupportItems
    ];

    for (const item of allItems) {
      await db.insert(faqItems).values(item).onConflictDoNothing();
    }

    console.log(`✅ Added ${allItems.length} sample FAQ items successfully`);
    console.log('🎉 Sample FAQ data migration completed successfully!');
  } catch (error) {
    console.error('❌ Error during sample FAQ data migration:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addSampleFaqData()
    .then(() => {
      console.log('✅ Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

export { addSampleFaqData };
