# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/storyworth

# JWT Secret for authentication
JWT_SECRET=your-super-secret-jwt-key-here

# Server Configuration
PORT=5000
NODE_ENV=development

# AWS S3 Configuration for file uploads
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-s3-bucket-name

# SendGrid Configuration for email notifications
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# OpenAI Configuration for AI features
OPENAI_API_KEY=your-openai-api-key

# Google Cloud Vision API for OCR
GOOGLE_APPLICATION_CREDENTIALS=./vision-api.json

# RPI Print API Configuration (Blurb's print-on-demand service)
# Get credentials from: https://api.sandbox.rpiprint.com
RPI_PRINT_API_KEY=your-rpi-print-api-key
RPI_PRINT_API_SECRET=your-rpi-print-api-secret
RPI_PRINT_ENVIRONMENT=sandbox

# Optional: Webhook Configuration for order status updates
RPI_PRINT_WEBHOOK_SECRET=your-webhook-secret
