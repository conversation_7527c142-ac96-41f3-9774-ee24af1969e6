import { Router, Request, Response } from 'express';
import { authMiddleware, AuthRequest } from '../middleware/auth.js';
import { supportService } from '../services/support.js';
import { z } from 'zod';

const router = Router();

// Validation schemas
const createTicketSchema = z.object({
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject too long'),
  description: z.string().min(1, 'Description is required').max(5000, 'Description too long'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
  category: z.string().optional(),
});

const addMessageSchema = z.object({
  message: z.string().min(1, 'Message is required').max(5000, 'Message too long'),
  isInternal: z.boolean().optional(),
  attachments: z.array(z.string()).optional(),
});

const updateTicketSchema = z.object({
  status: z.enum(['open', 'pending', 'solved', 'closed']),
  assignedToId: z.number().optional(),
});

// Create a new support ticket
router.post('/tickets', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const validatedData = createTicketSchema.parse(req.body);
    const user = req.user;

    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    console.log('Creating support ticket for user:', user.id, 'email:', user.email, 'name:', user.name);

    const ticket = await supportService.createTicket({
      userId: user.id,
      subject: validatedData.subject,
      description: validatedData.description,
      priority: validatedData.priority,
      category: validatedData.category,
      userEmail: user.email,
      userName: user.name,
    });

    res.status(201).json({
      success: true,
      ticket
    });
  } catch (error) {
    console.error('Error creating support ticket:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to create support ticket',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get tickets for the authenticated user
router.get('/tickets', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const status = req.query.status as string | undefined;

    // Regular users can only see their own tickets
    // Admins can see all tickets (handled in admin routes)
    const tickets = await supportService.getTickets(user.id, status);

    res.json({
      success: true,
      tickets
    });
  } catch (error) {
    console.error('Error fetching support tickets:', error);
    res.status(500).json({
      error: 'Failed to fetch support tickets',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get a specific ticket with messages
router.get('/tickets/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const ticketId = parseInt(req.params.id);
    if (isNaN(ticketId)) {
      return res.status(400).json({ error: 'Invalid ticket ID' });
    }

    const ticket = await supportService.getTicketWithMessages(ticketId);

    // Check if user owns this ticket or is an admin
    if (ticket.userId !== user.id && user.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({
      success: true,
      ticket
    });
  } catch (error) {
    console.error('Error fetching support ticket:', error);

    if (error instanceof Error && error.message === 'Ticket not found') {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    res.status(500).json({
      error: 'Failed to fetch support ticket',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Add a message to a ticket
router.post('/tickets/:id/messages', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const ticketId = parseInt(req.params.id);
    if (isNaN(ticketId)) {
      return res.status(400).json({ error: 'Invalid ticket ID' });
    }

    const validatedData = addMessageSchema.parse(req.body);

    // First check if user has access to this ticket
    const ticket = await supportService.getTicketWithMessages(ticketId);
    if (ticket.userId !== user.id && user.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    await supportService.addMessage({
      ticketId,
      authorId: user.id,
      authorType: user.role === 'admin' ? 'admin' : 'user',
      message: validatedData.message,
      isInternal: validatedData.isInternal || false,
      attachments: validatedData.attachments,
    });

    res.json({
      success: true,
      message: 'Message added successfully'
    });
  } catch (error) {
    console.error('Error adding message to support ticket:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    if (error instanceof Error && error.message === 'Ticket not found') {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    res.status(500).json({
      error: 'Failed to add message',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Admin routes for ticket management
router.get('/admin/tickets', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { status, assignedToId, page = 1, limit = 20, includeClosed = 'false' } = req.query;

    const tickets = await supportService.getTickets(
      undefined, // userId - not filtering by user for admin
      status as string,
      assignedToId ? parseInt(assignedToId as string) : undefined,
      includeClosed === 'true' // Include closed tickets if explicitly requested
    );

    // Add pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedTickets = tickets.slice(startIndex, endIndex);

    res.json({
      success: true,
      tickets: paginatedTickets,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: tickets.length,
        totalPages: Math.ceil(tickets.length / limitNum)
      }
    });
  } catch (error) {
    console.error('Error fetching admin support tickets:', error);
    res.status(500).json({
      error: 'Failed to fetch support tickets',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update ticket status (admin only)
router.patch('/admin/tickets/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const ticketId = parseInt(req.params.id);
    if (isNaN(ticketId)) {
      return res.status(400).json({ error: 'Invalid ticket ID' });
    }

    const validatedData = updateTicketSchema.parse(req.body);

    await supportService.updateTicketStatus(
      ticketId,
      validatedData.status,
      validatedData.assignedToId
    );

    res.json({
      success: true,
      message: 'Ticket updated successfully'
    });
  } catch (error) {
    console.error('Error updating support ticket:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    if (error instanceof Error && error.message === 'Ticket not found') {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    res.status(500).json({
      error: 'Failed to update ticket',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get support ticket statistics (admin only)
router.get('/admin/stats', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const stats = await supportService.getTicketStats();

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Error fetching support stats:', error);
    res.status(500).json({
      error: 'Failed to fetch support statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Test external service connection (admin only)
router.get('/admin/test-connection', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const connectionTest = await supportService.testConnection();

    res.json({
      success: true,
      connection: connectionTest
    });
  } catch (error) {
    console.error('Error testing support service connection:', error);
    res.status(500).json({
      error: 'Failed to test connection',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Sync external ticket data (admin only)
router.post('/admin/tickets/:id/sync', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const ticketId = parseInt(req.params.id);
    if (isNaN(ticketId)) {
      return res.status(400).json({ error: 'Invalid ticket ID' });
    }

    await supportService.syncExternalTicket(ticketId);

    res.json({
      success: true,
      message: 'Ticket synced successfully'
    });
  } catch (error) {
    console.error('Error syncing external ticket:', error);

    if (error instanceof Error && error.message === 'Ticket not found') {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    res.status(500).json({
      error: 'Failed to sync ticket',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create a test ticket (admin only) - for testing external service integration
router.post('/admin/test-ticket', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    console.log('Creating test support ticket for admin:', user.email);

    const ticket = await supportService.createTicket({
      userId: user.id,
      subject: 'Test Support Ticket',
      description: 'This is a test ticket created to verify external service integration is working correctly.',
      priority: 'normal',
      category: 'technical',
      userEmail: user.email,
      userName: user.name,
    });

    res.status(201).json({
      success: true,
      ticket,
      message: 'Test ticket created successfully'
    });
  } catch (error) {
    console.error('Error creating test support ticket:', error);
    res.status(500).json({
      error: 'Failed to create test ticket',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Send message to user (admin only)
router.post('/admin/tickets/:id/message', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const ticketId = parseInt(req.params.id);
    if (isNaN(ticketId)) {
      return res.status(400).json({ error: 'Invalid ticket ID' });
    }

    const { message, isInternal = false } = req.body;

    if (!message || !message.trim()) {
      return res.status(400).json({ error: 'Message is required' });
    }

    await supportService.addMessage({
      ticketId,
      authorId: user.id,
      authorType: 'admin',
      message: message.trim(),
      isInternal
    });

    res.json({
      success: true,
      message: 'Message sent successfully'
    });
  } catch (error) {
    console.error('Error sending message:', error);

    if (error instanceof Error && error.message === 'Ticket not found') {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    res.status(500).json({
      error: 'Failed to send message',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete closed tickets permanently (admin only)
router.delete('/admin/tickets/cleanup', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { olderThanDays = 30 } = req.query;
    const daysThreshold = parseInt(olderThanDays as string);

    if (isNaN(daysThreshold) || daysThreshold < 1) {
      return res.status(400).json({ error: 'Invalid days threshold' });
    }

    const deletedCount = await supportService.deleteClosedTickets(daysThreshold);

    res.json({
      success: true,
      message: `Successfully deleted ${deletedCount} closed tickets older than ${daysThreshold} days`,
      deletedCount
    });
  } catch (error) {
    console.error('Error deleting closed tickets:', error);
    res.status(500).json({
      error: 'Failed to delete closed tickets',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete a specific ticket permanently (admin only)
router.delete('/admin/tickets/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    const user = req.user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const ticketId = parseInt(req.params.id);
    if (isNaN(ticketId)) {
      return res.status(400).json({ error: 'Invalid ticket ID' });
    }

    await supportService.deleteTicket(ticketId);

    res.json({
      success: true,
      message: 'Ticket deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting ticket:', error);

    if (error instanceof Error && error.message === 'Ticket not found') {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    res.status(500).json({
      error: 'Failed to delete ticket',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
