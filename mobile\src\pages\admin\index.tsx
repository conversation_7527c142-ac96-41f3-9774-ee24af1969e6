import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  ActivityIndicator
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../../hooks/use-auth';
import { useLocation } from '../../lib/router';
import { useToast } from '../../hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { ProtectedRoute } from '../../components/auth/protected-route';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Colors, Spacing, BorderRadius, UserRole, API_URL } from '../../lib/constants';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Picker } from '@react-native-picker/picker';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface Project {
  id: number;
  name: string;
  description: string;
  status: string;
  organizer: {
    id: number;
    name: string;
    email: string;
  };
  contributors: Array<{
    user: {
      id: number;
      name: string;
      email: string;
    };
  }>;
}

interface SupportTicket {
  id: number;
  subject: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  userEmail: string;
  userName: string;
  externalTicketId: string | null;
  assignedToId: number | null;
  createdAt: string;
  updatedAt: string;
  resolvedAt: string | null;
}

export default function AdminPanel() {
  const { user } = useAuth();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState<Record<number, string>>({});
  const [deletingProjectId, setDeletingProjectId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<'users' | 'projects' | 'support'>('users');
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteDeadline, setInviteDeadline] = useState('');
  const [inviteReminderFrequency, setInviteReminderFrequency] = useState('weekly');

  // Support-related state
  const [supportStats, setSupportStats] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<any>(null);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isCreatingTestTicket, setIsCreatingTestTicket] = useState(false);
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [ticketsPagination, setTicketsPagination] = useState<any>(null);
  const [ticketsFilter, setTicketsFilter] = useState<string>('');
  const [isLoadingTickets, setIsLoadingTickets] = useState(false);

  const handleRoleChange = async (userId: number, newRole: string) => {
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/users/${userId}/role`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ role: newRole })
      });

      if (!response.ok) {
        throw new Error('Failed to update role');
      }

      setUsers(users.map(u =>
        u.id === userId ? { ...u, role: newRole } : u
      ));

      // Clear the selected role for this user after successful update
      setSelectedRoles(prev => {
        const newRoles = { ...prev };
        delete newRoles[userId];
        return newRoles;
      });

      toast({
        title: "Success",
        description: "User role updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (userId: number, force = false) => {
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/users/${userId}${force ? '?force=true' : ''}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.status === 409) {
        const error = await response.json();
        const { details } = error;

        // Show confirmation dialog
        const message = `This user has:
${details.recipeCount > 0 ? `- ${details.recipeCount} recipes\n` : ''}
${details.projectCount > 0 ? `- ${details.projectCount} projects\n` : ''}
Are you sure you want to delete this user and all their associated data?`;

        Alert.alert(
          'Confirm Deletion',
          message,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Delete',
              style: 'destructive',
              onPress: () => handleDeleteUser(userId, true)
            }
          ]
        );
        return;
      }

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete user');
      }

      setUsers(users.filter(u => u.id !== userId));
      toast({
        title: "Success",
        description: "User deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete user",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProject = async (projectId: number) => {
    try {
      setDeletingProjectId(projectId);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/projects/${projectId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete project');
      }

      // Update local state
      setProjects(projects.filter(p => p.id !== projectId));

      // Invalidate the recipe books query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["recipeBooks"] });

      toast({
        title: "Success",
        description: "Project deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete project",
        variant: "destructive",
      });
    } finally {
      setDeletingProjectId(null);
    }
  };

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/users`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      setUsers(data.users);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/projects`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch projects');
      }

      const data = await response.json();
      setProjects(data.projects);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch projects",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInviteContributor = async (project: Project) => {
    setSelectedProject(project);
    setIsInviteDialogOpen(true);
  };

  const handleSendInvite = async () => {
    if (!selectedProject) return;

    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/admin/projects/${selectedProject.id}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          email: inviteEmail,
          deadline: inviteDeadline ? new Date(inviteDeadline).toISOString() : undefined,
          reminderFrequency: inviteReminderFrequency
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to invite contributor');
      }

      // Invalidate the query to force a refresh
      queryClient.invalidateQueries({ queryKey: ["recipeBooks", user?.role] });
      setIsInviteDialogOpen(false);
      setInviteEmail('');
      setInviteDeadline('');
      setInviteReminderFrequency('weekly');
      toast({
        title: "Success",
        description: "Invitation sent successfully",
      });
    } catch (error) {
      console.error('Error inviting contributor:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send invitation",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Support-related functions
  const fetchSupportStats = async () => {
    try {
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/support/admin/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSupportStats(data.stats);
      }
    } catch (error) {
      console.error('Error fetching support stats:', error);
    }
  };

  const testConnection = async () => {
    try {
      setIsTestingConnection(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/support/admin/test-connection`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setConnectionStatus(data.connection);

        toast({
          title: data.connection.connected ? "Connection Successful" : "Connection Failed",
          description: data.connection.connected
            ? `Successfully connected to ${data.connection.service}`
            : data.connection.error,
          variant: data.connection.connected ? "default" : "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test connection",
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const createTestTicket = async () => {
    try {
      setIsCreatingTestTicket(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/support/admin/test-ticket`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Test Ticket Created",
          description: `Test ticket created successfully. ID: ${data.ticket.id}`,
        });

        // Refresh stats and tickets
        fetchSupportStats();
        fetchSupportTickets();
      } else {
        const error = await response.json();
        throw new Error(error.details || 'Failed to create test ticket');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create test ticket",
        variant: "destructive",
      });
    } finally {
      setIsCreatingTestTicket(false);
    }
  };

  const fetchSupportTickets = async (page = 1, status = '') => {
    try {
      setIsLoadingTickets(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10'
      });

      if (status) {
        params.append('status', status);
      }

      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/support/admin/tickets?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSupportTickets(data.tickets);
        setTicketsPagination(data.pagination);
      }
    } catch (error) {
      console.error('Error fetching support tickets:', error);
      toast({
        title: "Error",
        description: "Failed to fetch support tickets",
        variant: "destructive",
      });
    } finally {
      setIsLoadingTickets(false);
    }
  };

  const updateTicketStatus = async (ticketId: number, status: string) => {
    try {
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/support/admin/tickets/${ticketId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status })
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Ticket status updated successfully",
        });

        // Refresh tickets and stats
        fetchSupportTickets(ticketsPagination?.page || 1, ticketsFilter);
        fetchSupportStats();
      } else {
        const error = await response.json();
        throw new Error(error.details || 'Failed to update ticket status');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update ticket status",
        variant: "destructive",
      });
    }
  };

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return Colors.destructive;
      case 'pending': return Colors.warning;
      case 'solved': return Colors.success;
      case 'closed': return Colors.muted;
      default: return Colors.muted;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return Colors.destructive;
      case 'high': return Colors.warning;
      case 'normal': return Colors.foreground;
      case 'low': return Colors.muted;
      default: return Colors.muted;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  useEffect(() => {
    fetchUsers();
    fetchProjects();
    fetchSupportStats();
    fetchSupportTickets();
  }, []);

  return (
    <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Admin Dashboard</Text>
        </View>

        {/* Quick Actions Card */}
        <Card style={styles.card}>
          <CardHeader>
            <Text style={styles.cardTitle}>Quick Actions</Text>
            <Text style={styles.cardDescription}>Common administrative tasks</Text>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              onPress={() => setLocation('/admin/dashboard')}
              style={styles.quickActionButton}
            >
              <Icon name="check-circle" size={16} color={Colors.foreground} />
              <Text style={styles.quickActionText}>Review Recipe Submissions</Text>
            </Button>
          </CardContent>
        </Card>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <View style={styles.tabsList}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'users' && styles.activeTab]}
              onPress={() => setActiveTab('users')}
            >
              <Text style={[styles.tabText, activeTab === 'users' && styles.activeTabText]}>
                Users
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'projects' && styles.activeTab]}
              onPress={() => setActiveTab('projects')}
            >
              <Text style={[styles.tabText, activeTab === 'projects' && styles.activeTabText]}>
                Projects
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'support' && styles.activeTab]}
              onPress={() => setActiveTab('support')}
            >
              <Text style={[styles.tabText, activeTab === 'support' && styles.activeTabText]}>
                Support
              </Text>
            </TouchableOpacity>
          </View>

          {/* Users Tab Content */}
          {activeTab === 'users' && (
            <Card style={styles.card}>
              <CardHeader>
                <Text style={styles.cardTitle}>User Management</Text>
              </CardHeader>
              <CardContent>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>Users</Text>
                  <Button
                    variant="outline"
                    onPress={fetchUsers}
                    disabled={isLoading}
                    size="sm"
                  >
                    {isLoading ? "Loading..." : "Refresh Users"}
                  </Button>
                </View>

                <View style={styles.usersList}>
                  {users.map((user) => (
                    <View key={user.id} style={styles.userCard}>
                      <View style={styles.userInfo}>
                        <Text style={styles.userName}>{user.name}</Text>
                        <Text style={styles.userEmail}>{user.email}</Text>
                      </View>
                      <View style={styles.userActions}>
                        <View style={styles.roleSelector}>
                          <Text style={styles.roleLabel}>Role:</Text>
                          <View style={styles.pickerContainer}>
                            <Picker
                              selectedValue={selectedRoles[user.id] || user.role}
                              onValueChange={(value) => {
                                setSelectedRoles(prev => ({
                                  ...prev,
                                  [user.id]: value
                                }));
                                handleRoleChange(user.id, value);
                              }}
                              style={styles.picker}
                              enabled={!isLoading}
                            >
                              <Picker.Item label="Contributor" value="contributor" />
                              <Picker.Item label="Organizer" value="organizer" />
                              <Picker.Item label="Admin" value="admin" />
                            </Picker>
                          </View>
                        </View>
                        <Button
                          variant="destructive"
                          size="sm"
                          onPress={() => {
                            Alert.alert(
                              'Confirm Deletion',
                              'This action cannot be undone. This will permanently delete the user and all associated data.',
                              [
                                { text: 'Cancel', style: 'cancel' },
                                {
                                  text: 'Delete',
                                  style: 'destructive',
                                  onPress: () => handleDeleteUser(user.id)
                                }
                              ]
                            );
                          }}
                        >
                          Delete
                        </Button>
                      </View>
                    </View>
                  ))}
                </View>
              </CardContent>
            </Card>
          )}

          {/* Projects Tab Content */}
          {activeTab === 'projects' && (
            <Card style={styles.card}>
              <CardHeader>
                <Text style={styles.cardTitle}>Project Management</Text>
              </CardHeader>
              <CardContent>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>Projects</Text>
                  <Button
                    variant="outline"
                    onPress={fetchProjects}
                    disabled={isLoading}
                    size="sm"
                  >
                    {isLoading ? "Loading..." : "Refresh Projects"}
                  </Button>
                </View>

                <View style={styles.projectsList}>
                  {projects.map((project) => (
                    <View key={project.id} style={styles.projectCard}>
                      <View style={styles.projectInfo}>
                        <Text style={styles.projectName}>{project.name}</Text>
                        <Text style={styles.projectDescription}>{project.description}</Text>
                        <Text style={styles.projectDetail}>Status: {project.status}</Text>
                        <Text style={styles.projectDetail}>
                          Organizer: {project.organizer.name} ({project.organizer.email})
                        </Text>
                        <Text style={styles.projectDetail}>
                          Contributors: {project.contributors.length}
                        </Text>
                      </View>
                      <View style={styles.projectActions}>
                        <Button
                          variant="outline"
                          size="sm"
                          onPress={() => handleInviteContributor(project)}
                          style={styles.projectActionButton}
                        >
                          Invite Contributor
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          disabled={deletingProjectId === project.id}
                          onPress={() => {
                            Alert.alert(
                              'Confirm Deletion',
                              'This action cannot be undone. This will permanently delete the project and all associated data.',
                              [
                                { text: 'Cancel', style: 'cancel' },
                                {
                                  text: 'Delete',
                                  style: 'destructive',
                                  onPress: () => handleDeleteProject(project.id)
                                }
                              ]
                            );
                          }}
                        >
                          {deletingProjectId === project.id ? (
                            <>
                              <ActivityIndicator size="small" color={Colors.destructiveForeground} />
                              <Text style={styles.deletingText}>Deleting...</Text>
                            </>
                          ) : (
                            'Delete'
                          )}
                        </Button>
                      </View>
                    </View>
                  ))}
                </View>
              </CardContent>
            </Card>
          )}

          {/* Support Tab Content */}
          {activeTab === 'support' && (
            <Card style={styles.card}>
              <CardHeader>
                <Text style={styles.cardTitle}>Support Ticket Management</Text>
                <Text style={styles.cardDescription}>
                  Manage customer support tickets and external service integration
                </Text>
              </CardHeader>
              <CardContent>
                <View style={styles.supportContent}>
                  {/* Support Service Status */}
                  <View style={styles.statusCard}>
                    <View style={styles.statusHeader}>
                      <Text style={styles.statusTitle}>External Service Integration</Text>
                      <Button
                        variant="outline"
                        size="sm"
                        onPress={testConnection}
                        disabled={isTestingConnection}
                        style={styles.testButton}
                      >
                        {isTestingConnection ? "Testing..." : "Test Connection"}
                      </Button>
                    </View>
                    <View style={styles.statusIndicator}>
                      <View style={[
                        styles.statusDot,
                        { backgroundColor: connectionStatus?.connected ? '#10b981' : '#ef4444' }
                      ]} />
                      <Text style={styles.statusText}>
                        {connectionStatus ? (
                          connectionStatus.connected ? (
                            `Connected to ${connectionStatus.service}`
                          ) : (
                            `Failed to connect to ${connectionStatus.service}`
                          )
                        ) : (
                          'No external service configured'
                        )}
                      </Text>
                    </View>
                    {connectionStatus && !connectionStatus.connected && connectionStatus.error && (
                      <Text style={styles.errorText}>
                        Error: {connectionStatus.error}
                      </Text>
                    )}
                  </View>

                  {/* Quick Stats */}
                  <View style={styles.statsGrid}>
                    <View style={styles.statCard}>
                      <Text style={[styles.statNumber, { color: Colors.destructive }]}>
                        {supportStats?.open || 0}
                      </Text>
                      <Text style={styles.statLabel}>Open</Text>
                    </View>
                    <View style={styles.statCard}>
                      <Text style={[styles.statNumber, { color: Colors.warning }]}>
                        {supportStats?.pending || 0}
                      </Text>
                      <Text style={styles.statLabel}>Pending</Text>
                    </View>
                    <View style={styles.statCard}>
                      <Text style={[styles.statNumber, { color: Colors.success }]}>
                        {supportStats?.solved || 0}
                      </Text>
                      <Text style={styles.statLabel}>Solved</Text>
                    </View>
                    <View style={styles.statCard}>
                      <Text style={[styles.statNumber, { color: Colors.foreground }]}>
                        {supportStats?.total || 0}
                      </Text>
                      <Text style={styles.statLabel}>Total</Text>
                    </View>
                  </View>

                  {/* Support Actions */}
                  <View style={styles.supportActions}>
                    <Button
                      variant="outline"
                      style={styles.supportActionButton}
                      onPress={() => fetchSupportTickets(ticketsPagination?.page || 1, ticketsFilter)}
                      disabled={isLoadingTickets}
                    >
                      {isLoadingTickets ? "Loading..." : "Refresh"}
                    </Button>
                    <Button
                      variant="outline"
                      style={styles.supportActionButton}
                      onPress={createTestTicket}
                      disabled={isCreatingTestTicket}
                    >
                      {isCreatingTestTicket ? "Creating..." : "Create Test Ticket"}
                    </Button>
                  </View>

                  {/* Filter */}
                  <View style={styles.filterSection}>
                    <Text style={styles.filterLabel}>Filter by status:</Text>
                    <View style={styles.pickerContainer}>
                      <Picker
                        selectedValue={ticketsFilter}
                        onValueChange={(value) => {
                          setTicketsFilter(value);
                          fetchSupportTickets(1, value);
                        }}
                        style={styles.picker}
                      >
                        <Picker.Item label="All Tickets" value="" />
                        <Picker.Item label="Open" value="open" />
                        <Picker.Item label="Pending" value="pending" />
                        <Picker.Item label="Solved" value="solved" />
                        <Picker.Item label="Closed" value="closed" />
                      </Picker>
                    </View>
                  </View>

                  {/* Support Tickets List */}
                  <View style={styles.ticketsSection}>
                    <Text style={styles.sectionTitle}>Support Tickets</Text>
                    {isLoadingTickets ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color={Colors.primary} />
                        <Text style={styles.loadingText}>Loading tickets...</Text>
                      </View>
                    ) : supportTickets.length === 0 ? (
                      <View style={styles.emptyState}>
                        <Text style={styles.emptyStateText}>No support tickets found</Text>
                        <Text style={styles.emptyStateSubtext}>
                          Create a test ticket to see how the system works.
                        </Text>
                      </View>
                    ) : (
                      <View style={styles.ticketsList}>
                        {supportTickets.map((ticket) => (
                          <View key={ticket.id} style={styles.ticketCard}>
                            <View style={styles.ticketHeader}>
                              <Text style={styles.ticketId}>#{ticket.id}</Text>
                              <View style={styles.ticketBadges}>
                                <View style={[styles.badge, { backgroundColor: getStatusColor(ticket.status) }]}>
                                  <Text style={styles.badgeText}>{ticket.status}</Text>
                                </View>
                                <View style={[styles.badge, { backgroundColor: getPriorityColor(ticket.priority) }]}>
                                  <Text style={styles.badgeText}>{ticket.priority}</Text>
                                </View>
                                {ticket.externalTicketId && (
                                  <View style={[styles.badge, { backgroundColor: Colors.muted }]}>
                                    <Text style={styles.badgeText}>Synced</Text>
                                  </View>
                                )}
                              </View>
                            </View>
                            <Text style={styles.ticketSubject}>{ticket.subject}</Text>
                            <Text style={styles.ticketDescription} numberOfLines={2}>
                              {ticket.description}
                            </Text>
                            <View style={styles.ticketMeta}>
                              <Text style={styles.ticketMetaText}>
                                {ticket.userName} ({ticket.userEmail})
                              </Text>
                              <Text style={styles.ticketMetaText}>
                                {formatDate(ticket.createdAt)}
                              </Text>
                              {ticket.category && (
                                <View style={[styles.badge, { backgroundColor: Colors.muted }]}>
                                  <Text style={styles.badgeText}>{ticket.category}</Text>
                                </View>
                              )}
                            </View>
                            <View style={styles.ticketActions}>
                              <View style={styles.statusPickerContainer}>
                                <Picker
                                  selectedValue={ticket.status}
                                  onValueChange={(status) => updateTicketStatus(ticket.id, status)}
                                  style={styles.statusPicker}
                                >
                                  <Picker.Item label="Open" value="open" />
                                  <Picker.Item label="Pending" value="pending" />
                                  <Picker.Item label="Solved" value="solved" />
                                  <Picker.Item label="Closed" value="closed" />
                                </Picker>
                              </View>
                            </View>
                          </View>
                        ))}
                      </View>
                    )}

                    {/* Pagination */}
                    {ticketsPagination && ticketsPagination.totalPages > 1 && (
                      <View style={styles.pagination}>
                        <Text style={styles.paginationText}>
                          Showing {((ticketsPagination.page - 1) * ticketsPagination.limit) + 1} to{' '}
                          {Math.min(ticketsPagination.page * ticketsPagination.limit, ticketsPagination.total)} of{' '}
                          {ticketsPagination.total} tickets
                        </Text>
                        <View style={styles.paginationButtons}>
                          <Button
                            variant="outline"
                            size="sm"
                            onPress={() => fetchSupportTickets(ticketsPagination.page - 1, ticketsFilter)}
                            disabled={ticketsPagination.page <= 1 || isLoadingTickets}
                            style={styles.paginationButton}
                          >
                            Previous
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onPress={() => fetchSupportTickets(ticketsPagination.page + 1, ticketsFilter)}
                            disabled={ticketsPagination.page >= ticketsPagination.totalPages || isLoadingTickets}
                            style={styles.paginationButton}
                          >
                            Next
                          </Button>
                        </View>
                      </View>
                    )}
                  </View>
                </View>
              </CardContent>
            </Card>
          )}
        </View>

        {/* Invite Contributor Modal */}
        {isInviteDialogOpen && selectedProject && (
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Invite Contributor</Text>
                <Text style={styles.modalDescription}>
                  Invite a contributor to join this project.
                </Text>
              </View>

              <View style={styles.modalBody}>
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Email Address</Text>
                  <Input
                    value={inviteEmail}
                    onChangeText={setInviteEmail}
                    placeholder="Enter contributor's email"
                    keyboardType="email-address"
                    autoCapitalize="none"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Submission Deadline (Optional)</Text>
                  <Input
                    value={inviteDeadline}
                    onChangeText={setInviteDeadline}
                    placeholder="YYYY-MM-DD HH:MM"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Reminder Frequency</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={inviteReminderFrequency}
                      onValueChange={setInviteReminderFrequency}
                      style={styles.picker}
                    >
                      <Picker.Item label="Every 5 minutes (Test)" value="5min" />
                      <Picker.Item label="Every 15 minutes (Test)" value="15min" />
                      <Picker.Item label="Every 30 minutes (Test)" value="30min" />
                      <Picker.Item label="Every hour (Test)" value="1hour" />
                      <Picker.Item label="Daily" value="daily" />
                      <Picker.Item label="Weekly" value="weekly" />
                      <Picker.Item label="Bi-weekly" value="biweekly" />
                      <Picker.Item label="Monthly" value="monthly" />
                    </Picker>
                  </View>
                </View>
              </View>

              <View style={styles.modalFooter}>
                <Button
                  variant="outline"
                  onPress={() => setIsInviteDialogOpen(false)}
                  style={styles.modalButton}
                >
                  Cancel
                </Button>
                <Button
                  onPress={handleSendInvite}
                  disabled={isLoading || !inviteEmail.trim()}
                  style={styles.modalButton}
                >
                  {isLoading ? "Sending..." : "Send Invitation"}
                </Button>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </ProtectedRoute>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  card: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
    marginBottom: Spacing.xs,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  quickActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  quickActionText: {
    fontSize: 16,
    color: Colors.foreground,
    marginLeft: Spacing.sm,
  },
  tabsContainer: {
    margin: Spacing.lg,
    marginTop: 0,
  },
  tabsList: {
    flexDirection: 'row',
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
    padding: Spacing.xs,
    marginBottom: Spacing.lg,
  },
  tab: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.sm,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: Colors.card,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  activeTabText: {
    color: Colors.foreground,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
  },
  usersList: {
    gap: Spacing.md,
  },
  userCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.card,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  userActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  roleSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  roleLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.card,
    minWidth: 120,
  },
  picker: {
    color: Colors.foreground,
  },
  projectsList: {
    gap: Spacing.md,
  },
  projectCard: {
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.card,
  },
  projectInfo: {
    marginBottom: Spacing.md,
  },
  projectName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  projectDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
  },
  projectDetail: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  projectActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  projectActionButton: {
    flex: 1,
  },
  deletingText: {
    color: Colors.destructiveForeground,
    marginLeft: Spacing.xs,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    margin: Spacing.lg,
    maxWidth: 400,
    width: '90%',
  },
  modalHeader: {
    marginBottom: Spacing.lg,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  modalDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  modalBody: {
    marginBottom: Spacing.lg,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.sm,
  },
  modalButton: {
    minWidth: 100,
  },
  // Support section styles
  supportContent: {
    gap: Spacing.lg,
  },
  statusCard: {
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#10b981',
  },
  statusText: {
    fontSize: 14,
    color: Colors.foreground,
  },
  statusDescription: {
    fontSize: 12,
    color: Colors.mutedForeground,
    lineHeight: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    backgroundColor: Colors.card,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  supportActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  supportActionButton: {
    flex: 1,
  },
  ticketsSection: {
    gap: Spacing.md,
  },
  emptyState: {
    padding: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    backgroundColor: Colors.card,
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
  },
  emptyStateSubtext: {
    fontSize: 12,
    color: Colors.mutedForeground,
    textAlign: 'center',
    lineHeight: 16,
  },
  instructionsCard: {
    padding: Spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
    borderRadius: BorderRadius.md,
    backgroundColor: '#eff6ff',
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: Spacing.sm,
  },
  instructionsText: {
    fontSize: 14,
    color: '#1e40af',
    marginBottom: Spacing.sm,
  },
  instructionStep: {
    fontSize: 12,
    color: '#1e40af',
    marginBottom: Spacing.xs,
    paddingLeft: Spacing.md,
  },
  // New support styles
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  testButton: {
    minWidth: 100,
  },
  errorText: {
    fontSize: 12,
    color: Colors.destructive,
    marginTop: Spacing.sm,
  },
  filterSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  loadingContainer: {
    padding: Spacing.xl,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginTop: Spacing.sm,
  },
  ticketsList: {
    gap: Spacing.md,
  },
  ticketCard: {
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.card,
  },
  ticketHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  ticketId: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.foreground,
  },
  ticketBadges: {
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  badge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.background,
  },
  ticketSubject: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  ticketDescription: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
    lineHeight: 16,
  },
  ticketMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  ticketMetaText: {
    fontSize: 10,
    color: Colors.mutedForeground,
  },
  ticketActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  statusPickerContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.card,
    minWidth: 120,
  },
  statusPicker: {
    color: Colors.foreground,
    fontSize: 12,
  },
  pagination: {
    marginTop: Spacing.lg,
    gap: Spacing.md,
  },
  paginationText: {
    fontSize: 12,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  paginationButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Spacing.sm,
  },
  paginationButton: {
    minWidth: 80,
  },
});