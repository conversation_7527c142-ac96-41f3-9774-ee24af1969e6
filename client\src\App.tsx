import React, { useEffect } from 'react';
import { Route, Switch, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import NotFound from "@/pages/not-found";
import Design2 from "@/pages/designs/design2";
import { AuthProvider } from '@/hooks/use-auth';
import { ProtectedRoute } from '@/components/auth/protected-route';
import AuthForm from '@/components/auth/auth-form';
import { UserRole } from '@/lib/constants';
import RecipeBooks from '@/pages/recipe-books';
import CreateRecipeBook from '@/pages/recipe-books/create';
import CreateRecipe from '@/pages/recipes/create';
import AdminPanel from '@/pages/admin';
import AdminDashboard from '@/pages/admin/dashboard';
import OrganizerDashboard from '@/pages/organizer';
import AcceptInvite from '@/pages/accept-invite';
import ContributorDashboard from '@/pages/contributor/dashboard';
import ProjectDetails from '@/pages/recipe-books/[id]';
import EditRecipe from "@/pages/recipes/[id]/edit";
import { CookieConsent } from "@/components/privacy/cookie-consent";
import { PrivacyPolicyPage, HelpCenterPage, ContactPage } from "@/pages/privacy-policy";
import { DataRequestPage } from "@/pages/data-request";
import { TermsPage } from "@/pages/terms";
import OrderTracking from "@/pages/order-tracking";
import { CookiePreferencesPage } from "@/pages/cookie-preferences";
import { PricingCalculatorPage } from "@/pages/pricing-calculator";
import ErrorBoundary from "@/components/error-boundary";
import { LiveChatWidget, useLiveChatWidget } from "@/components/LiveChatWidget";
import SupportPage from "@/pages/support";

// Simple redirect component
const Redirect = ({ to }: { to: string }) => {
  const [, setLocation] = useLocation();

  useEffect(() => {
    setLocation(to);
  }, [to, setLocation]);

  return null;
};

function App() {
  const [location] = useLocation();
  const shouldShowChat = useLiveChatWidget();

  useEffect(() => {
    console.log('Current location:', location);
  }, [location]);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <AuthProvider>
            <Toaster />
            <div className="min-h-screen flex flex-col">
              <Header />
              <main className="flex-grow">
                <Switch>
                  <Route path="/" component={() => <Redirect to="/recipe-books" />} />
                  <Route path="/designs/design2" component={Design2} />
                  <Route path="/login" component={AuthForm} />
                  <Route path="/accept-invite" component={AcceptInvite} />

                  {/* Protected Routes */}
                  <Route path="/recipe-books">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                      <RecipeBooks />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/recipe-books/create">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                      <CreateRecipeBook />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/recipe-books/:id">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                      <ProjectDetails />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/recipes/create">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                      <CreateRecipe />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/recipes/:id/edit">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                      <EditRecipe />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/order-tracking">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                      <OrderTracking />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/admin">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
                      <AdminPanel />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/admin/dashboard">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER]}>
                      <AdminDashboard />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/organizer">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER]}>
                      <OrganizerDashboard />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/contributor">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                      <ContributorDashboard />
                    </ProtectedRoute>
                  </Route>

                  <Route path="/support">
                    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                      <SupportPage />
                    </ProtectedRoute>
                  </Route>

                  {/* Pricing Calculator */}
                  <Route path="/pricing-calculator" component={PricingCalculatorPage} />

                  {/* Privacy-related routes */}
                  <Route path="/privacy-policy" component={PrivacyPolicyPage} />
                  <Route path="/data-request" component={DataRequestPage} />
                  <Route path="/terms" component={TermsPage} />
                  <Route path="/cookie-preferences" component={CookiePreferencesPage} />

                  {/* Support routes */}
                  <Route path="/help" component={HelpCenterPage} />
                  <Route path="/contact" component={ContactPage} />

                  <Route path="*" component={NotFound} />
                </Switch>
              </main>
              <Footer />
            </div>
            <CookieConsent />
            {shouldShowChat && <LiveChatWidget />}
          </AuthProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
