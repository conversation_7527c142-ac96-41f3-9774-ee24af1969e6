# RPI Print API Testing Guide

This guide will help you test the RPI Print API (Blurb's print-on-demand service) to evaluate performance and quality before full integration.

## Quick Setup

### 1. Create RPI Print API Account
1. Visit [https://api.sandbox.rpiprint.com](https://api.sandbox.rpiprint.com)
2. Create a new account (free for testing)
3. Note: Your username becomes your `customer ID`

### 2. Get API Credentials
1. Login to the RPI Print API Dashboard
2. Click "API Credentials" in the left sidebar
3. Copy your API Key and Shared Secret

### 3. Configure Environment
1. Copy `server/.env.example` to `server/.env`
2. Add your RPI Print credentials:
```env
RPI_PRINT_API_KEY=your-api-key-here
RPI_PRINT_API_SECRET=your-shared-secret-here
RPI_PRINT_ENVIRONMENT=sandbox
```

### 4. Add Test Payment Method (Sandbox)
1. In the RPI Print Dashboard, go to "Payment Methods"
2. Add test credit card: `************** 1111`
3. Use any valid expiration date and CVV

## Running the Test

### Quick Test
```bash
cd server
npx tsx test-blurb-api.js
```

This will:
- ✅ Test API connection and authentication
- ✅ Create a sample book order using RPI Print's test PDFs
- ✅ Measure API response times
- ✅ Check order status
- ✅ Provide performance analysis

### Expected Output
```
🚀 Starting Blurb/RPI Print API Performance Test
=================================================

🧪 Testing RPI Print API - Creating Sample Order
================================================
📤 Sending order request to RPI Print API...
📍 Endpoint: https://open.api.sandbox.rpiprint.com/orders/create
📦 Order Items: 1
💰 Total Price: $25.99
✅ Order created successfully!
⏱️  Response time: 1234ms
📋 Order Details:
   Order ID: abc123-def456-ghi789
   Status: received
   Customer ID: your-customer-id

📊 Performance Analysis:
   API Response Time: 1234ms
   Status: 🟢 Fast

🔍 Checking order status for: abc123-def456-ghi789
==========================================
✅ Order status retrieved successfully!
⏱️  Response time: 456ms
📋 Order Status:
   Order ID: abc123-def456-ghi789
   Status: received
   Created: 2024-01-15T10:30:00Z
   Updated: 2024-01-15T10:30:00Z
```

## What This Tests

### 1. API Performance
- **Response Time**: How quickly orders are created
- **Reliability**: Whether the API is consistently available
- **Error Handling**: How the API responds to issues

### 2. Order Processing
- **Order Creation**: Can you successfully create print orders?
- **Status Tracking**: Can you track order progress?
- **Data Handling**: How well does the API handle order data?

### 3. Integration Readiness
- **Authentication**: Is your API setup working?
- **Network**: Are there any connectivity issues?
- **Configuration**: Are your credentials correct?

## Performance Benchmarks

### Response Times
- **🟢 Fast**: < 5 seconds
- **🟡 Moderate**: 5-10 seconds  
- **🔴 Slow**: > 10 seconds

### Typical Results
- Order creation: 1-3 seconds
- Status checks: 0.5-1 second
- File processing: Varies by PDF size

## Available Book Products

RPI Print offers several book formats perfect for recipe books:

### Popular Options for Recipe Books
1. **8x8 inches Photobook** - Square format, great for photos
2. **11x8.5 Imagewrap** - Landscape, good for recipes with images
3. **6x9 Trade Book** - Standard book size, professional look
4. **8.5x11 Magazine** - Large format, easy to read

### Pricing (Sandbox - Test Only)
- Prices in sandbox are for testing only
- Real pricing available in production environment
- Costs include printing, binding, and shipping

## Next Steps After Testing

### If Test Succeeds ✅
1. **Evaluate Performance**: Are response times acceptable?
2. **Check Quality**: Order a physical sample book
3. **Plan Integration**: Design your PDF generation system
4. **Consider Features**: What customization options do you need?

### If Test Fails ❌
1. **Check Credentials**: Verify API key and secret
2. **Network Issues**: Test internet connectivity
3. **Account Setup**: Ensure payment method is added
4. **Contact Support**: RPI Print has excellent support

## PDF Requirements for Recipe Books

When you're ready to generate your own PDFs:

### Technical Specs
- **Resolution**: 300 DPI minimum
- **Color Mode**: CMYK for print
- **Fonts**: Must be embedded
- **Bleed**: 0.125" on all sides
- **File Size**: Under 100MB per PDF

### Recipe Book Structure
- **Cover PDF**: Front and back cover design
- **Guts PDF**: Interior pages with recipes
- **Page Count**: Must match product specifications
- **Margins**: Follow RPI Print guidelines

## Cost Estimation

### Typical Recipe Book Costs
- **50-page book**: ~$15-25
- **100-page book**: ~$20-35
- **Shipping**: $3-8 depending on location
- **Rush orders**: Additional fees apply

### Volume Discounts
- Available for orders of 10+ copies
- Custom pricing for large volumes
- Contact RPI Print for enterprise pricing

## Support Resources

### Documentation
- [RPI Print API Docs](https://docs.api.rpiprint.com/)
- [Product Specifications](https://docs.api.rpiprint.com/products-main)
- [PDF Guidelines](https://docs.api.rpiprint.com/documentation/press-ready-PDFs)

### Getting Help
- **Email**: Support available through dashboard
- **Response Time**: Usually within 24 hours
- **Technical Issues**: Detailed error messages provided

## Security Notes

### Sandbox Environment
- ✅ Safe for testing - no real charges
- ✅ Test credit cards only
- ✅ Orders not actually printed
- ✅ Full API functionality available

### Production Environment
- ⚠️ Real credit card charges
- ⚠️ Actual book printing and shipping
- ⚠️ Requires valid payment method
- ⚠️ All orders are final

---

**Ready to test?** Run `npx tsx test-blurb-api.js` in the server directory!
