import { pgTable, serial, text, timestamp, integer, boolean, json, varchar, pgEnum } from 'drizzle-orm/pg-core';
import { relations, InferModel } from 'drizzle-orm';

// User roles enum
export const UserRole = {
  ADMIN: 'admin',
  ORGANIZER: 'organizer',
  CONTRIBUTOR: 'contributor'
} as const;

// Users table
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  name: text('name').notNull(),
  role: varchar('role', { length: 20 }).notNull().default(UserRole.CONTRIBUTOR),
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export type User = InferModel<typeof users>;
export type InsertUser = InferModel<typeof users, 'insert'>;

// User profiles for additional information
export const userProfiles = pgTable('user_profiles', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  phone: text('phone'),
  address: json('address'),
  preferences: json('preferences'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Projects table (Recipe Books)
export const projects = pgTable('projects', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  organizerId: integer('organizer_id').references(() => users.id),
  theme: text('theme').default('classic'),
  font: text('font').default('elegant'),
  chapterStyle: text('chapter_style').default('simple'),
  cover: text('cover').default('classic'),
  coverTitle: text('cover_title'),
  coverSubtitle: text('cover_subtitle'),
  coverImage: text('cover_image'),
  useCustomCoverImage: boolean('use_custom_cover_image').default(false),
  dedication: text('dedication'),
  includeDedication: boolean('include_dedication').default(true),
  includeQuotes: boolean('include_quotes').default(true),
  familyQuotes: text('family_quotes'), // Stored as JSON string
  status: text('status').default('draft'), // 'draft', 'in_progress', 'completed'
  role: text('role').default('organizer'), // 'admin', 'organizer', 'contributor'
  maxContributors: integer('max_contributors').notNull(),
  deadline: timestamp('deadline'),
  pricingTier: text('pricing_tier'), // For tier-based pricing
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Project Contributors table (many-to-many relationship)
export const projectContributors = pgTable('project_contributors', {
  id: serial('id').primaryKey(),
  projectId: integer('project_id').references(() => projects.id),
  userId: integer('user_id').references(() => users.id),
  status: text('status').default('pending'), // 'pending', 'accepted', 'rejected', 'removed'
  role: text('role').default('contributor'), // 'contributor', 'admin', 'organizer'
  invitationToken: text('invitation_token'),
  invitationSentAt: timestamp('invitation_sent_at'),
  invitationAcceptedAt: timestamp('invitation_accepted_at'),
  invitationExpiresAt: timestamp('invitation_expires_at'),
  deadline: timestamp('deadline'),
  lastReminderSentAt: timestamp('last_reminder_sent_at'),
  reminderFrequency: text('reminder_frequency').default('weekly').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Recipes table
export const recipes = pgTable('recipes', {
  id: serial('id').primaryKey(),
  projectId: integer('project_id').references(() => projects.id),
  contributorId: integer('contributor_id').references(() => users.id),
  title: text('title').notNull(),
  description: text('description'),
  category: text('category'),
  ingredients: json('ingredients').notNull(), // Array of { name, amount, unit }
  instructions: json('instructions').notNull(), // Array of steps
  measurementSystem: text('measurement_system').default('us'), // 'us' or 'metric'
  tags: json('tags'), // Array of strings
  images: json('images'), // Array of image URLs
  status: text('status').default('pending'), // 'pending', 'approved', 'rejected'
  role: text('role').default('contributor'), // 'contributor', 'admin', 'organizer'
  isOcrGenerated: boolean('is_ocr_generated').default(false),
  ocrSourceImage: text('ocr_source_image'),
  ocrRawText: text('ocr_raw_text'),
  ocrExtractedData: json('ocr_extracted_data'),
  isVoiceTranscribed: boolean('is_voice_transcribed').default(false),
  voiceSourceAudio: text('voice_source_audio'),
  voiceRawText: text('voice_raw_text'),
  voiceExtractedData: json('voice_extracted_data'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Recipe Comments table (for collaborative storytelling)
export const recipeComments = pgTable('recipe_comments', {
  id: serial('id').primaryKey(),
  recipeId: integer('recipe_id').references(() => recipes.id).notNull(),
  userId: integer('user_id').references(() => users.id).notNull(),
  comment: text('comment').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Notifications table
export const notifications = pgTable('notifications', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  type: text('type').notNull(), // 'recipe_rejected', 'recipe_approved', etc.
  message: text('message').notNull(),
  isRead: boolean('is_read').default(false),
  metadata: json('metadata'), // Additional data specific to the notification type
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Print Orders table for Blurb API integration
export const printOrders = pgTable('print_orders', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id).notNull(),
  blurbOrderId: text('blurb_order_id').notNull(),
  status: text('status').notNull().default('pending'), // 'pending', 'processing', 'printed', 'shipped', 'delivered', 'cancelled'
  bookData: json('book_data').notNull(), // Complete book data sent to Blurb
  shippingAddress: json('shipping_address').notNull(),
  trackingUrl: text('tracking_url'),
  shippingInfo: json('shipping_info'), // Tracking number, carrier, etc.
  estimatedDelivery: timestamp('estimated_delivery'),
  totalCost: text('total_cost'), // Store as string to avoid precision issues
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Support Tickets table for customer support integration
export const supportTickets = pgTable('support_tickets', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  externalTicketId: text('external_ticket_id'), // Zendesk/Intercom ticket ID
  subject: text('subject').notNull(),
  description: text('description').notNull(),
  status: text('status').notNull().default('open'), // 'open', 'pending', 'solved', 'closed'
  priority: text('priority').default('normal'), // 'low', 'normal', 'high', 'urgent'
  category: text('category'), // 'technical', 'billing', 'general', 'feature_request'
  assignedToId: integer('assigned_to_id').references(() => users.id),
  tags: json('tags'), // Array of tags
  metadata: json('metadata'), // Additional data from external service
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  resolvedAt: timestamp('resolved_at'),
});

// Support Ticket Messages table for conversation history
export const supportTicketMessages = pgTable('support_ticket_messages', {
  id: serial('id').primaryKey(),
  ticketId: integer('ticket_id').references(() => supportTickets.id).notNull(),
  authorId: integer('author_id').references(() => users.id),
  authorType: text('author_type').notNull(), // 'user', 'admin', 'system'
  message: text('message').notNull(),
  isInternal: boolean('is_internal').default(false), // Internal notes vs public messages
  attachments: json('attachments'), // Array of attachment URLs
  externalMessageId: text('external_message_id'), // External service message ID
  createdAt: timestamp('created_at').defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ one, many }) => ({
  profile: one(userProfiles, {
    fields: [users.id],
    references: [userProfiles.userId],
  }),
  projects: many(projects),
  contributedProjects: many(projectContributors),
  recipes: many(recipes),
  notifications: many(notifications),
  recipeComments: many(recipeComments),
  printOrders: many(printOrders),
}));

export const projectsRelations = relations(projects, ({ one, many }) => ({
  organizer: one(users, {
    fields: [projects.organizerId],
    references: [users.id],
  }),
  contributors: many(projectContributors),
  recipes: many(recipes),
}));

export const projectContributorsRelations = relations(projectContributors, ({ one }) => ({
  project: one(projects, {
    fields: [projectContributors.projectId],
    references: [projects.id],
  }),
  user: one(users, {
    fields: [projectContributors.userId],
    references: [users.id],
  }),
}));

export const recipesRelations = relations(recipes, ({ one, many }) => ({
  project: one(projects, {
    fields: [recipes.projectId],
    references: [projects.id],
  }),
  contributor: one(users, {
    fields: [recipes.contributorId],
    references: [users.id],
  }),
  comments: many(recipeComments),
}));

export const recipeCommentsRelations = relations(recipeComments, ({ one }) => ({
  recipe: one(recipes, {
    fields: [recipeComments.recipeId],
    references: [recipes.id],
  }),
  user: one(users, {
    fields: [recipeComments.userId],
    references: [users.id],
  }),
}));

export const notificationsRelations = relations(notifications, ({ one }) => ({
  user: one(users, {
    fields: [notifications.userId],
    references: [users.id],
  }),
}));

export const printOrdersRelations = relations(printOrders, ({ one }) => ({
  user: one(users, {
    fields: [printOrders.userId],
    references: [users.id],
  }),
}));

export const supportTicketsRelations = relations(supportTickets, ({ one, many }) => ({
  user: one(users, {
    fields: [supportTickets.userId],
    references: [users.id],
  }),
  assignedTo: one(users, {
    fields: [supportTickets.assignedToId],
    references: [users.id],
  }),
  messages: many(supportTicketMessages),
}));

export const supportTicketMessagesRelations = relations(supportTicketMessages, ({ one }) => ({
  ticket: one(supportTickets, {
    fields: [supportTicketMessages.ticketId],
    references: [supportTickets.id],
  }),
  author: one(users, {
    fields: [supportTicketMessages.authorId],
    references: [users.id],
  }),
}));