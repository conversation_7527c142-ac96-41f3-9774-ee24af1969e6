import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, X, Send, Minimize2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface ChatMessage {
  id: string;
  message: string;
  sender: 'user' | 'support';
  timestamp: Date;
}

interface LiveChatWidgetProps {
  isEnabled?: boolean;
  position?: 'bottom-right' | 'bottom-left';
}

export function LiveChatWidget({ isEnabled = true, position = 'bottom-right' }: LiveChatWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [hasStartedChat, setHasStartedChat] = useState(false);

  // Check if external chat service is configured
  useEffect(() => {
    // This would check if Zendesk Chat, Intercom, or other service is configured
    const checkChatService = () => {
      // For now, we'll simulate checking for external service
      const hasExternalService = process.env.REACT_APP_SUPPORT_SERVICE === 'zendesk' || 
                                 process.env.REACT_APP_SUPPORT_SERVICE === 'intercom';
      setIsConnected(hasExternalService);
    };

    checkChatService();
  }, []);

  const handleSendMessage = () => {
    if (!currentMessage.trim()) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      message: currentMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setCurrentMessage('');
    setHasStartedChat(true);

    // Simulate support response (in real implementation, this would go through the external service)
    setTimeout(() => {
      const supportMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message: "Thank you for contacting us! A support representative will be with you shortly. In the meantime, you can also check our Help Center for quick answers.",
        sender: 'support',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, supportMessage]);
    }, 1000);
  };

  const handleStartChat = () => {
    setHasStartedChat(true);
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      message: "Hello! How can we help you today?",
      sender: 'support',
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isEnabled) return null;

  const positionClasses = position === 'bottom-right' 
    ? 'bottom-4 right-4' 
    : 'bottom-4 left-4';

  return (
    <div className={`fixed ${positionClasses} z-50`}>
      {!isOpen ? (
        // Chat button
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-shadow"
          size="icon"
        >
          <MessageSquare className="h-6 w-6" />
        </Button>
      ) : (
        // Chat window
        <Card className={`w-80 h-96 shadow-xl transition-all duration-200 ${isMinimized ? 'h-12' : ''}`}>
          <CardHeader className="flex flex-row items-center justify-between p-4 bg-primary text-primary-foreground rounded-t-lg">
            <CardTitle className="text-sm font-medium">
              {isConnected ? 'Live Support Chat' : 'Support Chat'}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20"
                onClick={() => setIsMinimized(!isMinimized)}
              >
                <Minimize2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          {!isMinimized && (
            <CardContent className="p-0 flex flex-col h-80">
              {!hasStartedChat ? (
                // Welcome screen
                <div className="flex-1 flex flex-col items-center justify-center p-6 text-center">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="font-semibold mb-2">Need Help?</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Start a conversation with our support team. We're here to help!
                  </p>
                  {!isConnected && (
                    <p className="text-xs text-orange-600 mb-4">
                      Live chat is currently offline. Messages will be sent as support tickets.
                    </p>
                  )}
                  <Button onClick={handleStartChat} className="w-full">
                    Start Chat
                  </Button>
                </div>
              ) : (
                <>
                  {/* Messages area */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-3">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[80%] p-3 rounded-lg text-sm ${
                            message.sender === 'user'
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted'
                          }`}
                        >
                          <p>{message.message}</p>
                          <p className={`text-xs mt-1 opacity-70`}>
                            {message.timestamp.toLocaleTimeString([], { 
                              hour: '2-digit', 
                              minute: '2-digit' 
                            })}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Input area */}
                  <div className="border-t p-4">
                    <div className="flex gap-2">
                      <Textarea
                        value={currentMessage}
                        onChange={(e) => setCurrentMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="Type your message..."
                        className="flex-1 min-h-[40px] max-h-[80px] resize-none"
                        rows={1}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!currentMessage.trim()}
                        size="icon"
                        className="h-10 w-10"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                    {!isConnected && (
                      <p className="text-xs text-muted-foreground mt-2">
                        Messages will be converted to support tickets
                      </p>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          )}
        </Card>
      )}
    </div>
  );
}

// Hook to conditionally render the chat widget
export function useLiveChatWidget() {
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    // Only show chat widget on certain pages or for certain users
    const currentPath = window.location.pathname;
    const showOnPaths = ['/dashboard', '/recipe-books', '/help', '/contact'];
    
    setShouldShow(showOnPaths.some(path => currentPath.startsWith(path)));
  }, []);

  return shouldShow;
}
