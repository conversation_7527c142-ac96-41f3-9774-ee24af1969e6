import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/use-auth';

export function IntercomDebug() {
  const { user } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>({});

  const updateDebugInfo = () => {
    const info = {
      appId: import.meta.env.VITE_INTERCOM_APP_ID,
      accessToken: import.meta.env.VITE_INTERCOM_ACCESS_TOKEN ? 'Set' : 'Not Set',
      windowIntercom: !!window.Intercom,
      intercomSettings: window.intercomSettings,
      user: user ? {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      } : null,
      timestamp: new Date().toLocaleTimeString()
    };
    
    setDebugInfo(info);
    console.log('Intercom Debug Info:', info);
  };

  useEffect(() => {
    updateDebugInfo();
    const interval = setInterval(updateDebugInfo, 2000);
    return () => clearInterval(interval);
  }, [user]);

  const testIntercomMethods = () => {
    if (!window.Intercom) {
      alert('Intercom not loaded');
      return;
    }

    try {
      console.log('Testing Intercom methods...');
      
      // Test basic methods
      window.Intercom('show');
      setTimeout(() => window.Intercom('hide'), 2000);
      
      // Test getting unread count
      const unreadCount = window.Intercom('getUnreadCount');
      console.log('Unread count:', unreadCount);
      
      alert('Intercom methods test completed - check console');
    } catch (error) {
      console.error('Error testing Intercom methods:', error);
      alert('Error testing Intercom: ' + error);
    }
  };

  const reinitializeIntercom = () => {
    if (window.Intercom) {
      try {
        window.Intercom('shutdown');
        setTimeout(() => {
          const appId = import.meta.env.VITE_INTERCOM_APP_ID;
          window.Intercom('boot', { app_id: appId });
          console.log('Intercom reinitialized');
        }, 1000);
      } catch (error) {
        console.error('Error reinitializing Intercom:', error);
      }
    }
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Card className="fixed top-4 right-4 z-50 w-80 max-h-96 overflow-y-auto">
      <CardHeader>
        <CardTitle className="text-sm">Intercom Debug</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="text-xs space-y-1">
          <div><strong>App ID:</strong> {debugInfo.appId || 'Not Set'}</div>
          <div><strong>Access Token:</strong> {debugInfo.accessToken}</div>
          <div><strong>Window.Intercom:</strong> {debugInfo.windowIntercom ? 'Loaded' : 'Not Loaded'}</div>
          <div><strong>User:</strong> {debugInfo.user ? debugInfo.user.email : 'Not Logged In'}</div>
          <div><strong>Last Update:</strong> {debugInfo.timestamp}</div>
        </div>
        
        {debugInfo.intercomSettings && (
          <div className="text-xs">
            <strong>Settings:</strong>
            <pre className="bg-gray-100 p-1 rounded text-xs overflow-x-auto">
              {JSON.stringify(debugInfo.intercomSettings, null, 2)}
            </pre>
          </div>
        )}

        <div className="space-y-1">
          <Button size="sm" onClick={updateDebugInfo} className="w-full text-xs">
            Refresh Debug Info
          </Button>
          <Button size="sm" onClick={testIntercomMethods} className="w-full text-xs">
            Test Intercom Methods
          </Button>
          <Button size="sm" onClick={reinitializeIntercom} className="w-full text-xs">
            Reinitialize Intercom
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
