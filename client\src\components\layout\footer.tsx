import {
  BookOpen,
  Facebook,
  Instagram,
  Mail,
  Send,
  Shield,
  FileText,
  Cookie,
  HelpCircle,
  MessageSquare,
  ScrollText,
  ClipboardList,
  Mic,
  ScanLine,
  Printer
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link } from "wouter";

export function Footer() {
  return (
    <footer className="bg-gray-800 text-white py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <BookOpen className="h-6 w-6 text-secondary" />
              <span className="font-serif text-xl font-bold">RecipeBook</span>
            </div>
            <p className="text-sm opacity-70 mb-4">
              A collaborative cookbook platform for preserving family recipes and creating beautiful cookbooks.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-white opacity-70 hover:opacity-100">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-white opacity-70 hover:opacity-100">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="text-white opacity-70 hover:opacity-100">
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-4">Features</h3>
            <ul className="space-y-2 text-sm opacity-70">
              <li>
                <Link href="/features/recipe-submission" className="hover:text-secondary flex items-center gap-2">
                  <ClipboardList className="h-4 w-4" />
                  <span>Recipe Submission</span>
                </Link>
              </li>
              <li>
                <Link href="/features/voice-to-text" className="hover:text-secondary flex items-center gap-2">
                  <Mic className="h-4 w-4" />
                  <span>Voice-to-Text</span>
                </Link>
              </li>
              <li>
                <Link href="/features/ocr-upload" className="hover:text-secondary flex items-center gap-2">
                  <ScanLine className="h-4 w-4" />
                  <span>OCR Upload</span>
                </Link>
              </li>
              <li>
                <Link href="/features/print-on-demand" className="hover:text-secondary flex items-center gap-2">
                  <Printer className="h-4 w-4" />
                  <span>Print-on-Demand</span>
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-4">Legal & Privacy</h3>
            <ul className="space-y-2 text-sm opacity-70">
              <li>
                <Link href="/privacy-policy" className="hover:text-secondary flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  <span>Privacy Policy</span>
                </Link>
              </li>
              <li>
                <Link href="/data-request" className="hover:text-secondary flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  <span>Data Protection Rights</span>
                </Link>
              </li>
              <li>
                <Link href="/cookie-preferences" className="hover:text-secondary flex items-center gap-2">
                  <Cookie className="h-4 w-4" />
                  <span>Cookie Settings</span>
                </Link>
              </li>
              <li>
                <Link href="/terms" className="hover:text-secondary flex items-center gap-2">
                  <ScrollText className="h-4 w-4" />
                  <span>Terms of Service</span>
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-4">Support</h3>
            <ul className="space-y-2 text-sm opacity-70">
              <li>
                <Link href="/faq" className="hover:text-secondary flex items-center gap-2">
                  <HelpCircle className="h-4 w-4" />
                  <span>FAQ</span>
                </Link>
              </li>
              <li>
                <Link href="/help" className="hover:text-secondary flex items-center gap-2">
                  <HelpCircle className="h-4 w-4" />
                  <span>Help Center</span>
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-secondary flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  <span>Contact Us</span>
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-6 text-sm opacity-70 text-center">
          <div className="mb-2">
            We respect your privacy. View our{" "}
            <Link href="/privacy-policy" className="text-secondary hover:underline">
              Privacy Policy
            </Link>
            {" "}and{" "}
            <Link href="/data-request" className="text-secondary hover:underline">
              Data Protection Rights
            </Link>
          </div>
          <div>
            &copy; {new Date().getFullYear()} RecipeBook. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
}
