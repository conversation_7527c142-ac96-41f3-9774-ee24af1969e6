# Intercom Live Chat Integration

This document explains how to set up and use the Intercom live chat integration in RecipeBook.

## 🚀 Features Implemented

### Frontend (Client)
- **Intercom Messenger Widget**: Official Intercom chat widget with automatic user identification
- **Fallback System**: Falls back to custom chat widget if Intercom is not configured
- **User Tracking**: Automatic user identification and event tracking
- **Custom Events**: Track user actions like recipe creation, book creation, orders, etc.

### Backend (Server)
- **User Sync**: Automatically sync users to Intercom with custom attributes
- **Event Tracking**: Track custom events for analytics and user behavior
- **Conversation Management**: Create and manage conversations via API
- **Webhook Support**: Handle Intercom webhooks for real-time updates
- **Admin Tools**: Admin endpoints for managing conversations

## 🔧 Setup Instructions

### 1. Intercom Account Setup

1. **Create Intercom Account**: Go to [Intercom.com](https://www.intercom.com) and create an account
2. **Get App ID**: In your Intercom dashboard, go to Settings > Installation > Web to find your App ID
3. **Get Access Token**: Go to Settings > Developers > Developer Hub > Your App > Configure > Authentication to get your access token
4. **Set up Webhooks** (Optional): Go to Settings > Developers > Webhooks to configure webhook endpoints

### 2. Environment Variables

Add these variables to your environment files:

#### Server (.env)
```bash
INTERCOM_ACCESS_TOKEN=your-intercom-access-token
INTERCOM_WEBHOOK_SECRET=your-webhook-secret (optional)
```

#### Client (client/.env)
```bash
VITE_INTERCOM_APP_ID=your-intercom-app-id
VITE_INTERCOM_ACCESS_TOKEN=your-intercom-access-token
```

#### Root (.env)
```bash
INTERCOM_ACCESS_TOKEN=your-intercom-access-token
```

### 3. Current Configuration

Your environment is already configured with:
- **App ID**: `5d708733-6fde-4611-b3a4-325a51f18886`
- **Access Token**: Already set in your .env files

## 📱 How It Works

### Automatic User Identification
When a user logs in, they are automatically identified in Intercom with:
- Name and email
- User ID from your database
- Custom attributes (role, plan, app version)
- Registration date

### Event Tracking
The system automatically tracks important user events:
- Recipe creation (manual, voice, scan)
- Recipe book creation
- Invitations sent
- Orders placed
- Support tickets created
- Page views
- Feature usage

### Chat Widget
- **Intercom Widget**: If configured, shows the official Intercom messenger
- **Fallback Widget**: If Intercom is not configured, shows custom chat widget
- **Smart Positioning**: Appears on relevant pages (dashboard, recipe books, help pages)

## 🛠 Usage Examples

### Track Custom Events
```typescript
import { useIntercomEvents } from '@/hooks/use-intercom';

function MyComponent() {
  const { trackRecipeCreated, trackFeatureUsed } = useIntercomEvents();

  const handleRecipeCreate = (recipe) => {
    // Your recipe creation logic
    trackRecipeCreated(recipe);
  };

  const handleFeatureUse = () => {
    trackFeatureUsed('voice_recording', { duration: 30 });
  };
}
```

### Manual User Sync
```typescript
import { useIntercom } from '@/hooks/use-intercom';

function ProfileComponent() {
  const { syncUser } = useIntercom();

  const handleProfileUpdate = async () => {
    // Update profile logic
    await syncUser(); // Sync changes to Intercom
  };
}
```

### Admin Conversation Management
```typescript
import { IntercomAdmin } from '@/components/admin/IntercomAdmin';

function AdminDashboard() {
  return (
    <div>
      <h1>Admin Dashboard</h1>
      <IntercomAdmin />
    </div>
  );
}
```

## 🔗 API Endpoints

### User Endpoints
- `POST /api/intercom/sync-user` - Sync current user to Intercom
- `POST /api/intercom/track-event` - Track custom event
- `GET /api/intercom/conversations` - Get user's conversations
- `POST /api/intercom/conversations` - Create new conversation

### Admin Endpoints
- `GET /api/intercom/admin/conversations` - Get all conversations (admin only)
- `POST /api/intercom/admin/conversations/:id/reply` - Reply to conversation (admin only)

### Webhooks
- `POST /api/intercom/webhook` - Handle Intercom webhooks

## 🎯 Benefits

### For Users
- **Instant Support**: Real-time chat with support team
- **Context Aware**: Support team can see user's history and actions
- **Seamless Experience**: No need to leave the app for support

### For Support Team
- **Rich User Context**: See user's role, recent actions, and history
- **Automated Insights**: Track user behavior and common issues
- **Efficient Management**: Handle multiple conversations in one interface

### For Business
- **User Analytics**: Track feature usage and user journey
- **Support Metrics**: Measure response times and satisfaction
- **Proactive Support**: Identify and reach out to users who need help

## 🔍 Monitoring and Analytics

### Intercom Dashboard
- View all conversations and user interactions
- See user profiles with custom attributes
- Track events and user behavior
- Monitor support team performance

### Custom Events Tracked
- `recipe_created` - When users create recipes
- `book_created` - When users create recipe books
- `invite_sent` - When users invite contributors
- `order_placed` - When users place print orders
- `support_ticket_created` - When users create support tickets
- `page_viewed` - Page navigation tracking
- `feature_used` - Feature usage tracking

## 🚨 Troubleshooting

### Chat Widget Not Appearing
1. Check that `VITE_INTERCOM_APP_ID` is set in client/.env
2. Verify the App ID is correct in your Intercom dashboard
3. Check browser console for JavaScript errors

### User Not Identified
1. Ensure user is logged in
2. Check that access token has correct permissions
3. Verify user sync is working via `/api/intercom/sync-user`

### Events Not Tracking
1. Check that access token is valid
2. Verify user is authenticated
3. Check server logs for API errors

### Webhook Issues
1. Ensure webhook URL is accessible from internet
2. Verify webhook secret is correct
3. Check webhook signature validation

## 🔐 Security Notes

- Access tokens should be kept secure and not exposed in client-side code
- Webhook signatures should always be verified
- User data is automatically synced but can be controlled via privacy settings
- All API calls require user authentication

## 📞 Support

If you need help with the Intercom integration:
1. Check the Intercom documentation: https://developers.intercom.com/
2. Review the implementation in the codebase
3. Contact the development team for assistance
