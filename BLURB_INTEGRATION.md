# Blurb API Integration for Print-on-Demand Recipe Books

This document describes the Blurb API integration that allows users to order physical printed copies of their recipe books.

## Overview

The Blurb integration enables users to:
- Preview their recipe book with professional layouts
- Customize book appearance (themes, fonts, covers)
- Order physical printed books through Blurb's print-on-demand service
- Track order status and shipping information

## Features

### Frontend Features
- **Book Preview**: Interactive preview with pagination showing how the final book will look
- **Customization Options**: Multiple themes, fonts, chapter styles, and cover designs
- **Print Order Button**: "Order Print Book" button in the book preview
- **Shipping Address Form**: Validated form for collecting shipping information
- **Order Progress**: Real-time progress indicator during order creation
- **Error Handling**: User-friendly error messages and validation

### Backend Features
- **Blurb API Integration**: Complete integration with Blurb's print-on-demand API
- **Order Management**: Database storage of print orders with status tracking
- **Address Validation**: Validation of shipping addresses
- **Order Status Updates**: Automatic status updates from Blurb API
- **User Order History**: Endpoint to retrieve user's print order history

## Database Schema

### Print Orders Table
```sql
CREATE TABLE print_orders (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id),
  blurb_order_id TEXT NOT NULL UNIQUE,
  status TEXT NOT NULL DEFAULT 'pending',
  book_data JSONB NOT NULL,
  shipping_address JSONB NOT NULL,
  tracking_url TEXT,
  shipping_info JSONB,
  estimated_delivery TIMESTAMP,
  total_cost TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints

### Blurb Routes (`/api/blurb/`)

1. **GET /test** - Test Blurb API configuration
2. **POST /validate-address** - Validate shipping address
3. **POST /create-order** - Create a new print order
4. **GET /order/:orderId/status** - Get order status
5. **GET /orders** - Get user's order history

## Environment Configuration

Add these variables to your `.env` file:

```env
# Blurb API Configuration
BLURB_API_URL=https://api.rpiprint.com
BLURB_API_KEY=your-blurb-api-key
BLURB_ENVIRONMENT=sandbox

# Optional: Webhook configuration
BLURB_WEBHOOK_SECRET=your-blurb-webhook-secret
```

## Setup Instructions

### 1. Database Migration
Run the migration to create the print_orders table:
```bash
cd server
npx tsx migrations/0016_create_print_orders.ts
```

### 2. Install Dependencies
The required dependencies are already included:
- `axios` for HTTP requests to Blurb API
- Database schema updates for print orders

### 3. Configure Environment
1. Copy `server/.env.example` to `server/.env`
2. Add your Blurb API credentials
3. Set the appropriate environment (sandbox/production)

### 4. Test Integration
Use the test endpoint to verify configuration:
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:5000/api/blurb/test
```

## Usage Flow

1. **User creates recipes** in their recipe book
2. **User opens book preview** to see how the book will look
3. **User customizes book** appearance using the customization panel
4. **User clicks "Order Print Book"** button
5. **System shows shipping address form**
6. **User fills out shipping information** and submits
7. **System creates order** with Blurb API and saves to database
8. **User receives confirmation** with order ID and tracking information

## Book Formatting

The system automatically formats recipes into a professional book layout:
- **Cover page** with customizable design
- **Dedication page** (optional)
- **Family quotes pages** (optional)
- **Recipe pages** with ingredients and instructions
- **Automatic pagination** for long recipes
- **Professional typography** and spacing

## Order Status Tracking

Order statuses include:
- `pending` - Order submitted to Blurb
- `processing` - Order being processed
- `printed` - Book has been printed
- `shipped` - Book has been shipped
- `delivered` - Book has been delivered
- `cancelled` - Order was cancelled

## Error Handling

The system handles various error scenarios:
- Invalid shipping addresses
- Blurb API failures
- Network connectivity issues
- Authentication errors
- Validation errors

## Security Considerations

- All endpoints require authentication
- User can only access their own orders
- Shipping addresses are validated before submission
- API keys are stored securely in environment variables
- Order data is encrypted in the database

## Future Enhancements

Potential improvements:
- Webhook integration for real-time status updates
- Multiple book format options
- Bulk ordering for multiple copies
- Gift ordering with different shipping addresses
- Order cancellation functionality
- Cost estimation before ordering
