import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Animated
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Spacing, BorderRadius, API_URL } from '../../lib/constants';
import { useAuth } from '../../hooks/use-auth';
import { useToast } from '../../hooks/use-toast';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface FaqCategory {
  id: number;
  name: string;
  description: string;
  slug: string;
  icon: string;
  sortOrder: number;
  items: FaqItem[];
}

interface FaqItem {
  id: number;
  question: string;
  answer: string;
  slug: string;
  tags: string[];
  viewCount: number;
  isHelpful: number;
  isNotHelpful: number;
}

const iconMap: Record<string, string> = {
  BookOpen: 'book',
  ChefHat: 'restaurant',
  Book: 'library-books',
  Printer: 'print',
  CreditCard: 'credit-card',
  Settings: 'settings',
  HelpCircle: 'help'
};

export default function FaqPage() {
  const [categories, setCategories] = useState<FaqCategory[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [feedbackGiven, setFeedbackGiven] = useState<Set<number>>(new Set());
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (searchQuery.trim()) {
      searchFaqs();
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  }, [searchQuery]);

  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_URL}/faq/categories`);
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      } else {
        throw new Error('Failed to fetch FAQ categories');
      }
    } catch (error) {
      console.error('Error fetching FAQ categories:', error);
      Alert.alert('Error', 'Failed to load FAQ categories');
    } finally {
      setIsLoading(false);
    }
  };

  const searchFaqs = async () => {
    try {
      setIsSearching(true);
      const response = await fetch(`${API_URL}/faq/search?q=${encodeURIComponent(searchQuery)}&limit=20`);
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data);
      } else {
        throw new Error('Failed to search FAQs');
      }
    } catch (error) {
      console.error('Error searching FAQs:', error);
      Alert.alert('Error', 'Failed to search FAQs');
    } finally {
      setIsSearching(false);
    }
  };

  const toggleItem = (itemId: number) => {
    const newExpandedItems = new Set(expandedItems);
    if (newExpandedItems.has(itemId)) {
      newExpandedItems.delete(itemId);
    } else {
      newExpandedItems.add(itemId);
    }
    setExpandedItems(newExpandedItems);
  };

  const giveFeedback = async (itemId: number, helpful: boolean) => {
    if (feedbackGiven.has(itemId)) {
      Alert.alert('Feedback Already Given', 'You have already provided feedback for this FAQ item.');
      return;
    }

    try {
      const response = await fetch(`${API_URL}/faq/items/${itemId}/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ helpful }),
      });

      if (response.ok) {
        setFeedbackGiven(prev => new Set([...prev, itemId]));
        Alert.alert('Thank You!', 'Your feedback has been recorded.');
      } else {
        throw new Error('Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      Alert.alert('Error', 'Failed to submit feedback');
    }
  };

  const renderFaqItem = (item: FaqItem, categoryName?: string) => {
    const isExpanded = expandedItems.has(item.id);
    const hasFeedback = feedbackGiven.has(item.id);

    return (
      <View key={item.id} style={styles.faqItem}>
        <TouchableOpacity
          style={styles.faqHeader}
          onPress={() => toggleItem(item.id)}
          activeOpacity={0.7}
        >
          <View style={styles.faqHeaderContent}>
            <Text style={styles.faqQuestion}>{item.question}</Text>
            {categoryName && (
              <View style={styles.categoryBadge}>
                <Text style={styles.categoryBadgeText}>{categoryName}</Text>
              </View>
            )}
            <View style={styles.faqMeta}>
              <Text style={styles.viewCount}>{item.viewCount} views</Text>
              <Icon
                name={isExpanded ? 'expand-less' : 'expand-more'}
                size={24}
                color={Colors.mutedForeground}
              />
            </View>
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.faqContent}>
            <Text style={styles.faqAnswer}>{item.answer}</Text>
            
            {item.tags && item.tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {item.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            )}

            <View style={styles.feedbackContainer}>
              <Text style={styles.feedbackLabel}>Was this helpful?</Text>
              <View style={styles.feedbackButtons}>
                <TouchableOpacity
                  style={[styles.feedbackButton, hasFeedback && styles.feedbackButtonDisabled]}
                  onPress={() => giveFeedback(item.id, true)}
                  disabled={hasFeedback}
                >
                  <Icon name="thumb-up" size={16} color={hasFeedback ? Colors.mutedForeground : Colors.primary} />
                  <Text style={[styles.feedbackButtonText, hasFeedback && styles.feedbackButtonTextDisabled]}>
                    Yes ({item.isHelpful})
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.feedbackButton, hasFeedback && styles.feedbackButtonDisabled]}
                  onPress={() => giveFeedback(item.id, false)}
                  disabled={hasFeedback}
                >
                  <Icon name="thumb-down" size={16} color={hasFeedback ? Colors.mutedForeground : Colors.primary} />
                  <Text style={[styles.feedbackButtonText, hasFeedback && styles.feedbackButtonTextDisabled]}>
                    No ({item.isNotHelpful})
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading FAQ...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Frequently Asked Questions</Text>
        <Text style={styles.subtitle}>
          Find answers to common questions about RecipeBook. Can't find what you're looking for? Contact our support team.
        </Text>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color={Colors.mutedForeground} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search FAQ..."
            placeholderTextColor={Colors.mutedForeground}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Search Results */}
      {searchQuery.trim() ? (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Search Results {isSearching && <Text style={styles.searchingText}>(searching...)</Text>}
          </Text>
          {searchResults.length > 0 ? (
            <View>
              {searchResults.map((item) => renderFaqItem(item, item.categoryName))}
            </View>
          ) : !isSearching ? (
            <View style={styles.noResults}>
              <Icon name="search" size={48} color={Colors.mutedForeground} />
              <Text style={styles.noResultsText}>No results found for "{searchQuery}"</Text>
              <Text style={styles.noResultsSubtext}>Try different keywords or browse categories below</Text>
            </View>
          ) : null}
        </View>
      ) : (
        /* Categories */
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Browse by Category</Text>
          {categories.map((category) => {
            const iconName = iconMap[category.icon] || 'help';
            
            return (
              <View key={category.id} style={styles.category}>
                <View style={styles.categoryHeader}>
                  <View style={styles.categoryIcon}>
                    <Icon name={iconName} size={24} color={Colors.primary} />
                  </View>
                  <View style={styles.categoryInfo}>
                    <Text style={styles.categoryName}>{category.name}</Text>
                    <Text style={styles.categoryDescription}>{category.description}</Text>
                  </View>
                </View>
                <View style={styles.categoryItems}>
                  {category.items.map((item) => renderFaqItem(item))}
                  {category.items.length === 0 && (
                    <Text style={styles.noItemsText}>No FAQ items in this category yet.</Text>
                  )}
                </View>
              </View>
            );
          })}
        </View>
      )}

      {/* Still need help */}
      <View style={styles.helpSection}>
        <Icon name="chat" size={48} color={Colors.primary} />
        <Text style={styles.helpTitle}>Still need help?</Text>
        <Text style={styles.helpText}>
          Can't find the answer you're looking for? Our support team is here to help.
        </Text>
        <TouchableOpacity style={styles.contactButton}>
          <Text style={styles.contactButtonText}>Contact Support</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.mutedForeground,
  },
  header: {
    padding: Spacing.lg,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    lineHeight: 24,
  },
  searchContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.md,
  },
  searchIcon: {
    marginRight: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: Colors.foreground,
  },
  section: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },
  searchingText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    fontWeight: 'normal',
  },
  noResults: {
    alignItems: 'center',
    padding: Spacing.xl,
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  noResultsText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  noResultsSubtext: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  category: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    marginBottom: Spacing.lg,
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  categoryDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  categoryItems: {
    padding: Spacing.lg,
  },
  noItemsText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  faqItem: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    marginBottom: Spacing.md,
    overflow: 'hidden',
  },
  faqHeader: {
    padding: Spacing.md,
  },
  faqHeaderContent: {
    flex: 1,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
    lineHeight: 22,
  },
  categoryBadge: {
    alignSelf: 'flex-start',
    backgroundColor: Colors.secondary,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    marginBottom: Spacing.sm,
  },
  categoryBadgeText: {
    fontSize: 12,
    color: Colors.secondaryForeground,
    fontWeight: '500',
  },
  faqMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  viewCount: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  faqContent: {
    padding: Spacing.md,
    paddingTop: 0,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  faqAnswer: {
    fontSize: 14,
    color: Colors.foreground,
    lineHeight: 20,
    marginBottom: Spacing.md,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Spacing.md,
  },
  tag: {
    backgroundColor: Colors.muted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  tagText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  feedbackContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  feedbackLabel: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  feedbackButtons: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  feedbackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.background,
  },
  feedbackButtonDisabled: {
    opacity: 0.5,
  },
  feedbackButtonText: {
    fontSize: 12,
    color: Colors.primary,
    marginLeft: Spacing.xs,
    fontWeight: '500',
  },
  feedbackButtonTextDisabled: {
    color: Colors.mutedForeground,
  },
  helpSection: {
    alignItems: 'center',
    padding: Spacing.xl,
    margin: Spacing.lg,
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  helpTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  helpText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: Spacing.lg,
  },
  contactButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primaryForeground,
  },
});
