import { useCallback } from 'react';
import { useAuth } from './use-auth';
import { API_URL } from '@/lib/constants';

export function useIntercom() {
  const { user } = useAuth();

  // Track events in Intercom
  const trackEvent = useCallback(async (eventName: string, metadata?: Record<string, any>) => {
    try {
      const token = localStorage.getItem('token');
      if (!token || !user) {
        console.warn('User not authenticated, cannot track Intercom event');
        return;
      }

      const response = await fetch(`${API_URL}/intercom/track-event`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          event_name: eventName,
          metadata,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to track event');
      }

      console.log(`Tracked Intercom event: ${eventName}`, metadata);
    } catch (error) {
      console.error('Error tracking Intercom event:', error);
    }
  }, [user]);

  // Sync user to Intercom
  const syncUser = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token || !user) {
        console.warn('User not authenticated, cannot sync to Intercom');
        return;
      }

      const response = await fetch(`${API_URL}/intercom/sync-user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to sync user');
      }

      const data = await response.json();
      console.log('User synced to Intercom:', data);
      return data;
    } catch (error) {
      console.error('Error syncing user to Intercom:', error);
    }
  }, [user]);

  // Get user conversations
  const getConversations = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token || !user) {
        console.warn('User not authenticated, cannot fetch conversations');
        return [];
      }

      const response = await fetch(`${API_URL}/intercom/conversations`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversations');
      }

      const data = await response.json();
      return data.conversations || [];
    } catch (error) {
      console.error('Error fetching Intercom conversations:', error);
      return [];
    }
  }, [user]);

  // Create a new conversation
  const createConversation = useCallback(async (message: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token || !user) {
        console.warn('User not authenticated, cannot create conversation');
        return null;
      }

      const response = await fetch(`${API_URL}/intercom/conversations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          message,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create conversation');
      }

      const data = await response.json();
      return data.conversation;
    } catch (error) {
      console.error('Error creating Intercom conversation:', error);
      return null;
    }
  }, [user]);

  return {
    trackEvent,
    syncUser,
    getConversations,
    createConversation,
  };
}

// Predefined event tracking functions for common actions
export function useIntercomEvents() {
  const { trackEvent } = useIntercom();

  const trackRecipeCreated = useCallback((recipeData: any) => {
    trackEvent('recipe_created', {
      recipe_id: recipeData.id,
      recipe_title: recipeData.title,
      method: recipeData.method || 'manual', // manual, voice, scan
    });
  }, [trackEvent]);

  const trackBookCreated = useCallback((bookData: any) => {
    trackEvent('book_created', {
      book_id: bookData.id,
      book_name: bookData.name,
      theme: bookData.theme,
    });
  }, [trackEvent]);

  const trackInviteSent = useCallback((inviteData: any) => {
    trackEvent('invite_sent', {
      book_id: inviteData.bookId,
      invitee_email: inviteData.email,
      role: inviteData.role,
    });
  }, [trackEvent]);

  const trackOrderPlaced = useCallback((orderData: any) => {
    trackEvent('order_placed', {
      order_id: orderData.id,
      book_id: orderData.bookId,
      quantity: orderData.quantity,
      total_amount: orderData.totalAmount,
    });
  }, [trackEvent]);

  const trackSupportTicketCreated = useCallback((ticketData: any) => {
    trackEvent('support_ticket_created', {
      ticket_id: ticketData.id,
      category: ticketData.category,
      priority: ticketData.priority,
    });
  }, [trackEvent]);

  const trackPageView = useCallback((pageName: string, metadata?: Record<string, any>) => {
    trackEvent('page_viewed', {
      page_name: pageName,
      ...metadata,
    });
  }, [trackEvent]);

  const trackFeatureUsed = useCallback((featureName: string, metadata?: Record<string, any>) => {
    trackEvent('feature_used', {
      feature_name: featureName,
      ...metadata,
    });
  }, [trackEvent]);

  return {
    trackRecipeCreated,
    trackBookCreated,
    trackInviteSent,
    trackOrderPlaced,
    trackSupportTicketCreated,
    trackPageView,
    trackFeatureUsed,
  };
}
