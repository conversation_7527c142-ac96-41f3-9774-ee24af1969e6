import React from 'react';
import { View, Text, StyleSheet, ScrollView, Linking } from 'react-native';
import { Colors, Spacing } from '../lib/constants';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader } from '../components/ui/card';
import { useLocation } from 'wouter';

export function PrivacyPolicyPage() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Privacy Policy</Text>
      <Text style={styles.subtitle}>Privacy policy content</Text>
    </View>
  );
}

export function HelpCenterPage() {
  const [, setLocation] = useLocation();

  const handleContactPress = () => {
    setLocation('/contact');
  };

  const handleEmailPress = () => {
    Linking.openURL('mailto:<EMAIL>');
  };

  return (
    <ScrollView style={styles.scrollContainer}>
      <View style={styles.container}>
        <Text style={styles.title}>Help Center</Text>
        <Text style={styles.subtitle}>Find answers to common questions and get help with using RecipeBook</Text>

        {/* Quick Actions */}
        <View style={styles.section}>
          <View style={styles.cardRow}>
            <Card style={styles.card}>
              <CardHeader>
                <Text style={styles.cardTitle}>Contact Support</Text>
              </CardHeader>
              <CardContent>
                <Text style={styles.cardDescription}>
                  Need personalized help? Our support team is here to assist you.
                </Text>
                <Button onPress={handleContactPress} style={styles.button}>
                  <Text style={styles.buttonText}>Contact Us</Text>
                </Button>
              </CardContent>
            </Card>

            <Card style={styles.card}>
              <CardHeader>
                <Text style={styles.cardTitle}>Documentation</Text>
              </CardHeader>
              <CardContent>
                <Text style={styles.cardDescription}>
                  Browse our comprehensive guides and tutorials.
                </Text>
                <Button variant="outline" style={styles.button}>
                  <Text style={styles.buttonText}>View Guides</Text>
                </Button>
              </CardContent>
            </Card>
          </View>
        </View>

        {/* FAQ Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>

          <Card style={styles.faqCard}>
            <CardContent>
              <Text style={styles.faqQuestion}>How do I create a new recipe book?</Text>
              <Text style={styles.faqAnswer}>
                To create a new recipe book: Navigate to the Recipe Books page, click "Create New Book",
                fill in your book details, set the maximum number of contributors, and click "Create Book".
              </Text>
            </CardContent>
          </Card>

          <Card style={styles.faqCard}>
            <CardContent>
              <Text style={styles.faqQuestion}>How do I invite contributors?</Text>
              <Text style={styles.faqAnswer}>
                Open your recipe book, go to the "Collaborate" section, enter the contributor's email,
                set optional deadline and reminder frequency, then click "Send Invitation".
              </Text>
            </CardContent>
          </Card>

          <Card style={styles.faqCard}>
            <CardContent>
              <Text style={styles.faqQuestion}>What submission methods are available?</Text>
              <Text style={styles.faqAnswer}>
                RecipeBook supports manual entry, voice recording, photo upload, and OCR scanning
                for automatic text extraction from recipe images.
              </Text>
            </CardContent>
          </Card>
        </View>

        {/* Contact Section */}
        <Card style={styles.contactCard}>
          <CardContent>
            <Text style={styles.contactTitle}>Still need help?</Text>
            <Text style={styles.contactDescription}>
              Can't find what you're looking for? Our support team is ready to help.
            </Text>
            <View style={styles.contactButtons}>
              <Button onPress={handleContactPress} style={styles.button}>
                <Text style={styles.buttonText}>Contact Support</Text>
              </Button>
              <Button variant="outline" onPress={handleEmailPress} style={styles.button}>
                <Text style={styles.buttonText}>Email Us</Text>
              </Button>
            </View>
          </CardContent>
        </Card>
      </View>
    </ScrollView>
  );
}

export function ContactPage() {
  const handleEmailPress = () => {
    Linking.openURL('mailto:<EMAIL>');
  };

  const handleSupportEmailPress = () => {
    Linking.openURL('mailto:<EMAIL>?subject=Support Request');
  };

  const [, setLocation] = useLocation();

  const handleHelpPress = () => {
    setLocation('/help');
  };

  return (
    <ScrollView style={styles.scrollContainer}>
      <View style={styles.container}>
        <Text style={styles.title}>Contact Support</Text>
        <Text style={styles.subtitle}>
          Get in touch with our support team. We're here to help you with any questions or issues.
        </Text>

        {/* Contact Methods */}
        <View style={styles.section}>
          <View style={styles.cardRow}>
            <Card style={styles.card}>
              <CardHeader>
                <Text style={styles.cardTitle}>Email Support</Text>
              </CardHeader>
              <CardContent>
                <Text style={styles.cardDescription}>
                  Send us an email and we'll get back to you within 24 hours.
                </Text>
                <Button onPress={handleEmailPress} style={styles.button}>
                  <Text style={styles.buttonText}><EMAIL></Text>
                </Button>
              </CardContent>
            </Card>

            <Card style={styles.card}>
              <CardHeader>
                <Text style={styles.cardTitle}>Help Center</Text>
              </CardHeader>
              <CardContent>
                <Text style={styles.cardDescription}>
                  Browse our FAQ and documentation for quick answers.
                </Text>
                <Button variant="outline" onPress={handleHelpPress} style={styles.button}>
                  <Text style={styles.buttonText}>Browse Help Center</Text>
                </Button>
              </CardContent>
            </Card>
          </View>
        </View>

        {/* Support Form Placeholder */}
        <Card style={styles.supportCard}>
          <CardContent>
            <Text style={styles.supportTitle}>Submit a Support Request</Text>
            <Text style={styles.supportDescription}>
              For now, please use the email link above to contact our support team.
              We're working on implementing an integrated support ticket system with Zendesk/Intercom.
            </Text>
            <View style={styles.supportButtons}>
              <Button onPress={handleSupportEmailPress} style={styles.button}>
                <Text style={styles.buttonText}>Email Support</Text>
              </Button>
              <Button variant="outline" onPress={handleHelpPress} style={styles.button}>
                <Text style={styles.buttonText}>View FAQ</Text>
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Emergency Contact */}
        <View style={styles.emergencyCard}>
          <Text style={styles.emergencyTitle}>Urgent Issues</Text>
          <Text style={styles.emergencyText}>
            For urgent technical issues or account problems, please email us directly at
            <EMAIL> with "URGENT" in the subject line.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  container: {
    flex: 1,
    padding: Spacing.lg,
    backgroundColor: Colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  section: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  cardRow: {
    flexDirection: 'column',
    gap: Spacing.md,
  },
  card: {
    backgroundColor: Colors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    padding: Spacing.md,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  button: {
    marginTop: Spacing.sm,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  faqCard: {
    backgroundColor: Colors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    padding: Spacing.md,
    marginBottom: Spacing.md,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  faqAnswer: {
    fontSize: 14,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  contactCard: {
    backgroundColor: Colors.muted,
    borderRadius: 8,
    padding: Spacing.md,
    marginTop: Spacing.lg,
  },
  contactTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  contactDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  contactButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  supportCard: {
    backgroundColor: Colors.muted,
    borderRadius: 8,
    padding: Spacing.md,
    marginTop: Spacing.lg,
  },
  supportTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  supportDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  supportButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  emergencyCard: {
    backgroundColor: '#fef3c7',
    borderLeftWidth: 4,
    borderLeftColor: '#f59e0b',
    borderRadius: 8,
    padding: Spacing.md,
    marginTop: Spacing.lg,
  },
  emergencyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400e',
    marginBottom: Spacing.sm,
  },
  emergencyText: {
    fontSize: 14,
    color: '#b45309',
    lineHeight: 20,
  },
});
