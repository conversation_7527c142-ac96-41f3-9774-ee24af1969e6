import { Link, useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { Menu, BookOpen, LogOut, User, Settings, Trash2, Package } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { UserRole, API_URL } from "@/lib/constants";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { Notifications } from '../Notifications';

type NavLinkProps = {
  href: string;
  label: string;
  isActive: boolean;
};

const NavLink = ({ href, label, isActive }: NavLinkProps) => {
  return (
    <Link href={href}>
      <span
        className={`font-medium py-2 border-b-2 transition-colors cursor-pointer ${
          isActive
            ? "border-secondary text-foreground"
            : "border-transparent hover:border-secondary/70 text-foreground/80 hover:text-foreground"
        }`}
      >
        {label}
      </span>
    </Link>
  );
};

export function Header() {
  const [location] = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const { user, logout } = useAuth();
  const { toast } = useToast();

  const navigation = [
    { href: "/recipe-books", label: "Recipe Books", roles: [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR] },
    { href: "/recipe-books/create", label: "Create Book", roles: [UserRole.ADMIN, UserRole.ORGANIZER] },
    { href: "/recipes/create", label: "Add Recipe", roles: [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR] },
    { href: "/order-tracking", label: "Order Tracking", roles: [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR] },
    { href: "/pricing-calculator", label: "Pricing", roles: [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR] },
    { href: "/admin", label: "Admin Panel", roles: [UserRole.ADMIN] },
    { href: "/admin/dashboard", label: "Recipe Approvals", roles: [UserRole.ADMIN, UserRole.ORGANIZER] },
    { href: "/organizer", label: "Organizer Dashboard", roles: [UserRole.ORGANIZER] },
    { href: "/contributor", label: "My Recipes", roles: [UserRole.CONTRIBUTOR] },
  ];

  const filteredNavigation = navigation.filter(item => {
    if (!user?.role) return false;
    const userRole = user.role.toLowerCase();
    return item.roles.includes(userRole as typeof UserRole[keyof typeof UserRole]);
  });

  const isActivePath = (path: string) => {
    return location === path;
  };

  const handleDeleteAccount = async () => {
    try {
      const response = await fetch(`${API_URL}/auth/me`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete account');
      }

      logout();
      toast({
        title: "Success",
        description: "Account deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete account",
        variant: "destructive",
      });
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <BookOpen className="h-6 w-6 text-secondary" />
          <span className="font-serif text-2xl font-bold">RecipeBook</span>
        </div>

        <div className="flex items-center space-x-6">
          <nav className="hidden md:flex items-center space-x-8">
            {filteredNavigation.map((item) => (
              <NavLink
                key={item.href}
                href={item.href}
                label={item.label}
                isActive={isActivePath(item.href)}
              />
            ))}
          </nav>

          <Notifications />

          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <User className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={logout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                      <Trash2 className="mr-2 h-4 w-4" />
                      <span>Delete Account</span>
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete your
                        account and remove your data from our servers.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDeleteAccount}>
                        Continue
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Link href="/login">
              <Button variant="ghost" size="sm">
                Log in
              </Button>
            </Link>
          )}

          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                aria-label="Open Menu"
              >
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <nav className="flex flex-col gap-4">
                {filteredNavigation.map((item) => {
                  const handleClick = () => setIsOpen(false);
                  return (
                    <div key={item.href} onClick={handleClick}>
                      <NavLink
                        href={item.href}
                        label={item.label}
                        isActive={isActivePath(item.href)}
                      />
                    </div>
                  );
                })}
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
