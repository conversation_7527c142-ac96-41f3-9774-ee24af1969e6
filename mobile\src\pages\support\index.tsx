import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Spacing, BorderRadius, API_URL } from '../../lib/constants';
import { useAuth } from '../../hooks/use-auth';
import { useToast } from '../../hooks/use-toast';
import Icon from 'react-native-vector-icons/MaterialIcons';

export default function SupportPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    subject: '',
    description: '',
    priority: 'normal',
    category: 'general'
  });
  const { user } = useAuth();
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!formData.subject.trim() || !formData.description.trim()) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    try {
      setIsSubmitting(true);
      const token = await AsyncStorage.getItem('token');
      
      const response = await fetch(`${API_URL}/support/tickets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const data = await response.json();
        Alert.alert(
          "Support Ticket Created",
          `Your ticket #${data.ticket.id} has been created. We'll respond within 24 hours.`,
          [{ text: "OK" }]
        );
        
        // Reset form
        setFormData({
          subject: '',
          description: '',
          priority: 'normal',
          category: 'general'
        });
      } else {
        const error = await response.json();
        throw new Error(error.details || 'Failed to create support ticket');
      }
    } catch (error) {
      Alert.alert(
        "Error",
        error instanceof Error ? error.message : "Failed to create support ticket"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Header */}
      <View style={styles.header}>
        <Icon name="support" size={32} color={Colors.primary} />
        <Text style={styles.title}>Support Center</Text>
        <Text style={styles.subtitle}>
          Need help? We're here to assist you. Submit a support ticket below.
        </Text>
      </View>

      {/* Support Ticket Form */}
      <View style={styles.formContainer}>
        <Text style={styles.sectionTitle}>Create Support Ticket</Text>
        
        {/* Category and Priority */}
        <View style={styles.row}>
          <View style={styles.halfWidth}>
            <Text style={styles.label}>Category</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={formData.category}
                onValueChange={(value) => handleInputChange('category', value)}
                style={styles.picker}
              >
                <Picker.Item label="General Support" value="general" />
                <Picker.Item label="Technical Issue" value="technical" />
                <Picker.Item label="Billing & Account" value="billing" />
                <Picker.Item label="Feature Request" value="feature" />
                <Picker.Item label="Bug Report" value="bug" />
              </Picker>
            </View>
          </View>
          
          <View style={styles.halfWidth}>
            <Text style={styles.label}>Priority</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={formData.priority}
                onValueChange={(value) => handleInputChange('priority', value)}
                style={styles.picker}
              >
                <Picker.Item label="Low" value="low" />
                <Picker.Item label="Normal" value="normal" />
                <Picker.Item label="High" value="high" />
                <Picker.Item label="Urgent" value="urgent" />
              </Picker>
            </View>
          </View>
        </View>

        {/* Subject */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Subject *</Text>
          <TextInput
            style={styles.input}
            value={formData.subject}
            onChangeText={(value) => handleInputChange('subject', value)}
            placeholder="Brief description of your issue"
            placeholderTextColor={Colors.mutedForeground}
          />
        </View>

        {/* Description */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description *</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={formData.description}
            onChangeText={(value) => handleInputChange('description', value)}
            placeholder="Please provide detailed information about your issue..."
            placeholderTextColor={Colors.mutedForeground}
            multiline
            numberOfLines={6}
            textAlignVertical="top"
          />
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <View style={styles.submitButtonContent}>
              <ActivityIndicator size="small" color={Colors.primaryForeground} />
              <Text style={styles.submitButtonText}>Creating Ticket...</Text>
            </View>
          ) : (
            <Text style={styles.submitButtonText}>Create Support Ticket</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Contact Information */}
      <View style={styles.contactContainer}>
        <Text style={styles.sectionTitle}>Contact Information</Text>
        
        <View style={styles.contactItem}>
          <Icon name="email" size={20} color={Colors.mutedForeground} />
          <View style={styles.contactText}>
            <Text style={styles.contactLabel}>Email Support</Text>
            <Text style={styles.contactValue}><EMAIL></Text>
          </View>
        </View>
        
        <View style={styles.contactItem}>
          <Icon name="phone" size={20} color={Colors.mutedForeground} />
          <View style={styles.contactText}>
            <Text style={styles.contactLabel}>Phone Support</Text>
            <Text style={styles.contactValue}>1-800-RECIPE-1</Text>
            <Text style={styles.contactHours}>Mon-Fri 9AM-5PM EST</Text>
          </View>
        </View>
      </View>

      {/* Quick Help */}
      <View style={styles.helpContainer}>
        <Text style={styles.sectionTitle}>Quick Help</Text>
        <Text style={styles.helpSubtitle}>Common Issues:</Text>
        <Text style={styles.helpItem}>• Recipe upload problems</Text>
        <Text style={styles.helpItem}>• Book creation issues</Text>
        <Text style={styles.helpItem}>• Account access problems</Text>
        <Text style={styles.helpItem}>• Billing questions</Text>
        
        <View style={styles.responseTime}>
          <Text style={styles.responseTimeText}>
            <Text style={styles.responseTimeBold}>Response Time:</Text> We typically respond to support tickets within 24 hours.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  contentContainer: {
    padding: Spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    lineHeight: 22,
  },
  formContainer: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },
  row: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginBottom: Spacing.lg,
  },
  halfWidth: {
    flex: 1,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.background,
  },
  picker: {
    height: 50,
    color: Colors.foreground,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
    backgroundColor: Colors.background,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.md,
    padding: Spacing.lg,
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  submitButtonText: {
    color: Colors.primaryForeground,
    fontSize: 16,
    fontWeight: '600',
  },
  contactContainer: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.lg,
    gap: Spacing.md,
  },
  contactText: {
    flex: 1,
  },
  contactLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: 2,
  },
  contactValue: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  contactHours: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginTop: 2,
  },
  helpContainer: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  helpSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  helpItem: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: 4,
    lineHeight: 20,
  },
  responseTime: {
    marginTop: Spacing.lg,
    paddingTop: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  responseTimeText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  responseTimeBold: {
    fontWeight: '600',
    color: Colors.foreground,
  },
});
