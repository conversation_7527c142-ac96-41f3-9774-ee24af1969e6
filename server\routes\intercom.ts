import { Router } from 'express';
import { authMiddleware } from '../middleware/auth.js';
import { intercomService } from '../services/intercom.js';
import { db } from '../db.js';
import { users } from '../schema.js';
import { eq } from 'drizzle-orm';

const router = Router();

// Sync current user to Intercom
router.post('/sync-user', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Get user from database
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Sync to Intercom
    const intercomUser = await intercomService.syncUser(user[0]);
    
    res.json({ 
      success: true, 
      message: 'User synced to Intercom successfully',
      intercom_user_id: intercomUser.id 
    });
  } catch (error) {
    console.error('Error syncing user to Intercom:', error);
    res.status(500).json({ 
      error: 'Failed to sync user to Intercom',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Track custom events
router.post('/track-event', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { event_name, metadata } = req.body;
    if (!event_name) {
      return res.status(400).json({ error: 'Event name is required' });
    }

    await intercomService.trackEvent(userId.toString(), event_name, metadata);
    
    res.json({ 
      success: true, 
      message: 'Event tracked successfully' 
    });
  } catch (error) {
    console.error('Error tracking Intercom event:', error);
    res.status(500).json({ 
      error: 'Failed to track event',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get user conversations
router.get('/conversations', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const conversations = await intercomService.getConversations(userId.toString());
    
    res.json({ 
      success: true, 
      conversations: conversations.conversations || []
    });
  } catch (error) {
    console.error('Error fetching Intercom conversations:', error);
    res.status(500).json({ 
      error: 'Failed to fetch conversations',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create a new conversation
router.post('/conversations', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { message, message_type = 'comment' } = req.body;
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    const conversation = await intercomService.createConversation({
      from: {
        type: 'user',
        id: userId.toString()
      },
      body: message,
      message_type
    });
    
    res.json({ 
      success: true, 
      conversation 
    });
  } catch (error) {
    console.error('Error creating Intercom conversation:', error);
    res.status(500).json({ 
      error: 'Failed to create conversation',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Webhook endpoint for Intercom events
router.post('/webhook', async (req, res) => {
  try {
    const signature = req.headers['x-hub-signature'] as string;
    const webhookSecret = process.env.INTERCOM_WEBHOOK_SECRET;
    
    if (!webhookSecret) {
      console.warn('Intercom webhook secret not configured');
      return res.status(200).json({ received: true });
    }

    // Verify webhook signature
    const body = JSON.stringify(req.body);
    const isValid = intercomService.verifyWebhook(body, signature, webhookSecret);
    
    if (!isValid) {
      console.error('Invalid Intercom webhook signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    const { topic, data } = req.body;
    
    console.log(`Received Intercom webhook: ${topic}`, data);

    // Handle different webhook events
    switch (topic) {
      case 'conversation.user.created':
      case 'conversation.user.replied':
        // Handle new conversations or replies
        console.log('New conversation or reply:', data.item);
        break;
        
      case 'conversation.admin.replied':
        // Handle admin replies
        console.log('Admin replied to conversation:', data.item);
        break;
        
      case 'conversation.admin.closed':
        // Handle conversation closure
        console.log('Conversation closed:', data.item);
        break;
        
      case 'user.created':
        // Handle new user creation
        console.log('New user created:', data.item);
        break;
        
      default:
        console.log(`Unhandled webhook topic: ${topic}`);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Error processing Intercom webhook:', error);
    res.status(500).json({ 
      error: 'Failed to process webhook',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Admin endpoints (require admin role)
router.get('/admin/conversations', authMiddleware, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user?.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const conversations = await intercomService.getConversations();
    
    res.json({ 
      success: true, 
      conversations: conversations.conversations || []
    });
  } catch (error) {
    console.error('Error fetching all Intercom conversations:', error);
    res.status(500).json({ 
      error: 'Failed to fetch conversations',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.post('/admin/conversations/:id/reply', authMiddleware, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user?.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { id } = req.params;
    const { message, message_type = 'comment' } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    const reply = await intercomService.replyToConversation(id, message, message_type);
    
    res.json({ 
      success: true, 
      reply 
    });
  } catch (error) {
    console.error('Error replying to Intercom conversation:', error);
    res.status(500).json({ 
      error: 'Failed to reply to conversation',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
