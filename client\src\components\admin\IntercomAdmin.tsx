import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Send, RefreshCw, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { API_URL } from '@/lib/constants';

interface Conversation {
  id: string;
  type: string;
  state: 'open' | 'closed' | 'snoozed';
  subject?: string;
  created_at: number;
  updated_at: number;
  waiting_since?: number;
  snoozed_until?: number;
  source?: {
    type: string;
    id: string;
    delivered_as: string;
    subject: string;
    body: string;
    author: {
      type: 'admin' | 'user' | 'contact';
      id: string;
      name?: string;
      email?: string;
    };
    attachments: any[];
    url?: string;
    redacted: boolean;
  };
  contacts?: {
    type: string;
    contacts: <PERSON><PERSON><PERSON><{
      type: 'contact' | 'user';
      id: string;
      name?: string;
      email?: string;
      role?: string;
    }>;
  };
  teammates?: {
    type: string;
    teammates: Array<{
      type: 'admin';
      id: string;
      name?: string;
      email?: string;
    }>;
  };
  conversation_parts?: {
    type: string;
    conversation_parts: Array<{
      type: string;
      id: string;
      part_type: 'comment' | 'note' | 'assignment';
      body?: string;
      created_at: number;
      updated_at: number;
      notified_at: number;
      assigned_to?: any;
      author: {
        type: 'admin' | 'user' | 'contact' | 'bot';
        id: string;
        name?: string;
        email?: string;
      };
      attachments: any[];
      external_id?: string;
      redacted: boolean;
    }>;
    total_count: number;
  };
  conversation_rating?: any;
  tags?: {
    type: string;
    tags: any[];
  };
  custom_attributes?: Record<string, any>;
}

export function IntercomAdmin() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [replyMessage, setReplyMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  const fetchConversations = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/intercom/admin/conversations`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversations');
      }

      const data = await response.json();
      setConversations(data.conversations || []);
    } catch (error) {
      console.error('Error fetching conversations:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch conversations',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const sendReply = async () => {
    if (!selectedConversation || !replyMessage.trim()) return;

    try {
      setIsSending(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/intercom/admin/conversations/${selectedConversation.id}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          message: replyMessage,
          message_type: 'comment',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send reply');
      }

      toast({
        title: 'Success',
        description: 'Reply sent successfully',
      });

      setReplyMessage('');
      // Refresh conversations to show the new reply
      await fetchConversations();
    } catch (error) {
      console.error('Error sending reply:', error);
      toast({
        title: 'Error',
        description: 'Failed to send reply',
        variant: 'destructive',
      });
    } finally {
      setIsSending(false);
    }
  };

  const deleteConversation = async (conversationId: string) => {
    if (!confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
      return;
    }

    try {
      setIsDeleting(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/intercom/admin/conversations/${conversationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete conversation');
      }

      toast({
        title: 'Success',
        description: 'Conversation deleted successfully',
      });

      // Clear selected conversation if it was the deleted one
      if (selectedConversation?.id === conversationId) {
        setSelectedConversation(null);
      }

      // Refresh conversations list
      await fetchConversations();
    } catch (error) {
      console.error('Error deleting conversation:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete conversation',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  useEffect(() => {
    fetchConversations();
  }, []);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const getStateColor = (state: string) => {
    switch (state) {
      case 'open':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      case 'snoozed':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function to get user information from conversation
  const getUserInfo = (conversation: Conversation) => {
    // Try to get user from contacts first
    if (conversation.contacts?.contacts && conversation.contacts.contacts.length > 0) {
      const contact = conversation.contacts.contacts[0];
      return {
        name: contact.name,
        email: contact.email,
        id: contact.id
      };
    }

    // Fallback to source author if available
    if (conversation.source?.author) {
      return {
        name: conversation.source.author.name,
        email: conversation.source.author.email,
        id: conversation.source.author.id
      };
    }

    return {
      name: 'Anonymous',
      email: '',
      id: ''
    };
  };

  // Helper function to get the first message preview
  const getMessagePreview = (conversation: Conversation) => {
    // Filter out default Intercom messages
    const isDefaultMessage = (body: string) => {
      const defaultMessages = [
        '<p>This is new message</p>',
        'This is new message',
        '<p>New conversation</p>',
        'New conversation'
      ];
      return defaultMessages.some(msg => body.includes(msg));
    };

    // Check conversation parts first (actual user messages)
    if (conversation.conversation_parts?.conversation_parts && conversation.conversation_parts.conversation_parts.length > 0) {
      for (const part of conversation.conversation_parts.conversation_parts) {
        if (part.body && !isDefaultMessage(part.body)) {
          const cleanBody = part.body.replace(/<[^>]*>/g, ''); // Strip HTML tags
          return cleanBody.substring(0, 100) + (cleanBody.length > 100 ? '...' : '');
        }
      }
    }

    // Fallback to source if no valid conversation parts
    if (conversation.source?.body && !isDefaultMessage(conversation.source.body)) {
      const cleanBody = conversation.source.body.replace(/<[^>]*>/g, ''); // Strip HTML tags
      return cleanBody.substring(0, 100) + (cleanBody.length > 100 ? '...' : '');
    }

    return 'No message content';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        Loading conversations...
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[600px]">
      {/* Conversations List */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Intercom Conversations
          </CardTitle>
          <Button variant="outline" size="sm" onClick={fetchConversations}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="p-0">
          <div className="max-h-[500px] overflow-y-auto">
            {conversations.length === 0 ? (
              <div className="p-6 text-center text-muted-foreground">
                No conversations found
              </div>
            ) : (
              conversations.map((conversation) => {
                const userInfo = getUserInfo(conversation);
                const messagePreview = getMessagePreview(conversation);

                return (
                  <div
                    key={conversation.id}
                    className={`p-4 border-b hover:bg-muted/50 transition-colors ${
                      selectedConversation?.id === conversation.id ? 'bg-muted' : ''
                    }`}
                  >
                    <div
                      className="cursor-pointer"
                      onClick={() => setSelectedConversation(conversation)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <p className="font-medium text-sm">
                            {userInfo.name || userInfo.email || 'Anonymous'}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {userInfo.email}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                            {messagePreview}
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-1">
                          <Badge className={getStateColor(conversation.state)}>
                            {conversation.state}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {conversation.conversation_parts?.total_count || 0} messages
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Updated: {formatDate(conversation.updated_at)}
                      </p>
                    </div>

                    {/* Delete button */}
                    <div className="flex justify-end mt-2 pt-2 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteConversation(conversation.id);
                        }}
                        disabled={isDeleting}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>

      {/* Conversation Details */}
      <Card>
        <CardHeader>
          <CardTitle>
            {selectedConversation ? 'Conversation Details' : 'Select a Conversation'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedConversation ? (
            <div className="space-y-4">
              {/* Conversation Info */}
              <div className="p-3 bg-muted rounded-lg">
                {(() => {
                  const userInfo = getUserInfo(selectedConversation);
                  return (
                    <>
                      <p className="font-medium">
                        {userInfo.name || userInfo.email || 'Anonymous'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {userInfo.email}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Created: {formatDate(selectedConversation.created_at)}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className={getStateColor(selectedConversation.state)}>
                          {selectedConversation.state}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {selectedConversation.conversation_parts?.total_count || 0} messages
                        </span>
                      </div>
                    </>
                  );
                })()}
              </div>

              {/* Messages */}
              <div className="max-h-[300px] overflow-y-auto space-y-3">
                {(() => {
                  const isDefaultMessage = (body: string) => {
                    const defaultMessages = [
                      '<p>This is new message</p>',
                      'This is new message',
                      '<p>New conversation</p>',
                      'New conversation'
                    ];
                    return defaultMessages.some(msg => body.includes(msg));
                  };

                  const messages = [];

                  // Add source message if it's not a default message
                  if (selectedConversation.source &&
                      selectedConversation.source.body &&
                      !isDefaultMessage(selectedConversation.source.body)) {
                    messages.push({
                      id: 'source',
                      body: selectedConversation.source.body,
                      author: selectedConversation.source.author,
                      created_at: selectedConversation.created_at,
                      isSource: true
                    });
                  }

                  // Add conversation parts that are not default messages
                  if (selectedConversation.conversation_parts?.conversation_parts) {
                    selectedConversation.conversation_parts.conversation_parts.forEach(part => {
                      if (part.body && !isDefaultMessage(part.body)) {
                        messages.push({
                          id: part.id,
                          body: part.body,
                          author: part.author,
                          created_at: part.created_at,
                          isSource: false
                        });
                      }
                    });
                  }

                  // Sort messages by creation time
                  messages.sort((a, b) => a.created_at - b.created_at);

                  if (messages.length === 0) {
                    return (
                      <p className="text-muted-foreground text-center">No messages found</p>
                    );
                  }

                  return messages.map((message) => (
                    <div
                      key={message.id}
                      className={`p-3 rounded-lg ${
                        message.author.type === 'admin'
                          ? 'bg-blue-50 border-l-4 border-blue-500'
                          : 'bg-gray-50 border-l-4 border-gray-500'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-sm">
                          {message.author.name || message.author.email || message.author.type}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(message.created_at)}
                        </span>
                      </div>
                      <div
                        className="text-sm"
                        dangerouslySetInnerHTML={{ __html: message.body }}
                      />
                    </div>
                  ));
                })()}
              </div>

              {/* Reply Form */}
              {selectedConversation.state === 'open' && (
                <div className="space-y-3">
                  <Textarea
                    placeholder="Type your reply..."
                    value={replyMessage}
                    onChange={(e) => setReplyMessage(e.target.value)}
                    rows={3}
                  />
                  <Button
                    onClick={sendReply}
                    disabled={!replyMessage.trim() || isSending}
                    className="w-full"
                  >
                    {isSending ? (
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Send className="h-4 w-4 mr-2" />
                    )}
                    Send Reply
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Select a conversation to view details and reply</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
