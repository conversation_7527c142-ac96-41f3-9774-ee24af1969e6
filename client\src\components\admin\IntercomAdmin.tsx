import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Send, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { API_URL } from '@/lib/constants';

interface Conversation {
  id: string;
  state: 'open' | 'closed' | 'snoozed';
  subject?: string;
  created_at: number;
  updated_at: number;
  user?: {
    id: string;
    name?: string;
    email?: string;
  };
  conversation_parts?: {
    conversation_parts: Array<{
      id: string;
      part_type: 'comment' | 'note';
      body: string;
      created_at: number;
      author: {
        type: 'admin' | 'user';
        name?: string;
        email?: string;
      };
    }>;
  };
}

export function IntercomAdmin() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [replyMessage, setReplyMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const { toast } = useToast();

  const fetchConversations = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/intercom/admin/conversations`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversations');
      }

      const data = await response.json();
      setConversations(data.conversations || []);
    } catch (error) {
      console.error('Error fetching conversations:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch conversations',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const sendReply = async () => {
    if (!selectedConversation || !replyMessage.trim()) return;

    try {
      setIsSending(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/intercom/admin/conversations/${selectedConversation.id}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          message: replyMessage,
          message_type: 'comment',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send reply');
      }

      toast({
        title: 'Success',
        description: 'Reply sent successfully',
      });

      setReplyMessage('');
      // Refresh conversations to show the new reply
      await fetchConversations();
    } catch (error) {
      console.error('Error sending reply:', error);
      toast({
        title: 'Error',
        description: 'Failed to send reply',
        variant: 'destructive',
      });
    } finally {
      setIsSending(false);
    }
  };

  useEffect(() => {
    fetchConversations();
  }, []);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const getStateColor = (state: string) => {
    switch (state) {
      case 'open':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      case 'snoozed':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        Loading conversations...
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[600px]">
      {/* Conversations List */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Intercom Conversations
          </CardTitle>
          <Button variant="outline" size="sm" onClick={fetchConversations}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="p-0">
          <div className="max-h-[500px] overflow-y-auto">
            {conversations.length === 0 ? (
              <div className="p-6 text-center text-muted-foreground">
                No conversations found
              </div>
            ) : (
              conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={`p-4 border-b cursor-pointer hover:bg-muted/50 transition-colors ${
                    selectedConversation?.id === conversation.id ? 'bg-muted' : ''
                  }`}
                  onClick={() => setSelectedConversation(conversation)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <p className="font-medium text-sm">
                        {conversation.user?.name || conversation.user?.email || 'Anonymous'}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {conversation.user?.email}
                      </p>
                    </div>
                    <Badge className={getStateColor(conversation.state)}>
                      {conversation.state}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(conversation.updated_at)}
                  </p>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Conversation Details */}
      <Card>
        <CardHeader>
          <CardTitle>
            {selectedConversation ? 'Conversation Details' : 'Select a Conversation'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedConversation ? (
            <div className="space-y-4">
              {/* Conversation Info */}
              <div className="p-3 bg-muted rounded-lg">
                <p className="font-medium">
                  {selectedConversation.user?.name || selectedConversation.user?.email || 'Anonymous'}
                </p>
                <p className="text-sm text-muted-foreground">
                  Created: {formatDate(selectedConversation.created_at)}
                </p>
                <Badge className={getStateColor(selectedConversation.state)}>
                  {selectedConversation.state}
                </Badge>
              </div>

              {/* Messages */}
              <div className="max-h-[300px] overflow-y-auto space-y-3">
                {selectedConversation.conversation_parts?.conversation_parts?.map((part) => (
                  <div
                    key={part.id}
                    className={`p-3 rounded-lg ${
                      part.author.type === 'admin'
                        ? 'bg-blue-50 border-l-4 border-blue-500'
                        : 'bg-gray-50 border-l-4 border-gray-500'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-sm">
                        {part.author.name || part.author.email || part.author.type}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatDate(part.created_at)}
                      </span>
                    </div>
                    <div
                      className="text-sm"
                      dangerouslySetInnerHTML={{ __html: part.body }}
                    />
                  </div>
                )) || (
                  <p className="text-muted-foreground text-center">No messages found</p>
                )}
              </div>

              {/* Reply Form */}
              {selectedConversation.state === 'open' && (
                <div className="space-y-3">
                  <Textarea
                    placeholder="Type your reply..."
                    value={replyMessage}
                    onChange={(e) => setReplyMessage(e.target.value)}
                    rows={3}
                  />
                  <Button
                    onClick={sendReply}
                    disabled={!replyMessage.trim() || isSending}
                    className="w-full"
                  >
                    {isSending ? (
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Send className="h-4 w-4 mr-2" />
                    )}
                    Send Reply
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Select a conversation to view details and reply</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
