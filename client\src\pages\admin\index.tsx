import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { UserRole } from "@/lib/constants";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { useQueryClient } from "@tanstack/react-query";
import { Loader2, FileCheck, MessageSquare, RefreshCw, TestTube, CheckCircle, XCircle, Clock, User, AlertCircle, ChevronLeft, ChevronRight, Mail, Send, Trash2 } from "lucide-react";
import { Link } from "wouter";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { API_URL } from '@/lib/constants';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface Project {
  id: number;
  name: string;
  description: string;
  status: string;
  organizer: {
    id: number;
    name: string;
    email: string;
  };
  contributors: Array<{
    user: {
      id: number;
      name: string;
      email: string;
    };
  }>;
}

interface SupportTicket {
  id: number;
  subject: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  userEmail: string;
  userName: string;
  externalTicketId: string | null;
  assignedToId: number | null;
  createdAt: string;
  updatedAt: string;
  resolvedAt: string | null;
}

export default function AdminPanel() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState<Record<number, string>>({});
  const [deletingProjectId, setDeletingProjectId] = useState<number | null>(null);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteDeadline, setInviteDeadline] = useState('');
  const [inviteReminderFrequency, setInviteReminderFrequency] = useState('weekly');

  // Support-related state
  const [supportStats, setSupportStats] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<any>(null);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isCreatingTestTicket, setIsCreatingTestTicket] = useState(false);
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [ticketsPagination, setTicketsPagination] = useState<any>(null);
  const [ticketsFilter, setTicketsFilter] = useState<string>('all');
  const [isLoadingTickets, setIsLoadingTickets] = useState(false);

  const [selectedTicketForContact, setSelectedTicketForContact] = useState<SupportTicket | null>(null);
  const [contactMessage, setContactMessage] = useState('');
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [isDeletingTickets, setIsDeletingTickets] = useState(false);

  const handleRoleChange = async (userId: number, newRole: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_URL}/admin/users/${userId}/role`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ role: newRole })
      });

      if (!response.ok) {
        throw new Error('Failed to update role');
      }

      setUsers(users.map(u =>
        u.id === userId ? { ...u, role: newRole } : u
      ));

      // Clear the selected role for this user after successful update
      setSelectedRoles(prev => {
        const newRoles = { ...prev };
        delete newRoles[userId];
        return newRoles;
      });

      toast({
        title: "Success",
        description: "User role updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (userId: number, force = false) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_URL}/admin/users/${userId}${force ? '?force=true' : ''}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.status === 409) {
        const error = await response.json();
        const { details } = error;

        // Show confirmation dialog
        const message = `This user has:
${details.recipeCount > 0 ? `- ${details.recipeCount} recipes\n` : ''}
${details.projectCount > 0 ? `- ${details.projectCount} projects\n` : ''}
Are you sure you want to delete this user and all their associated data?`;

        if (window.confirm(message)) {
          // If confirmed, retry with force=true
          return handleDeleteUser(userId, true);
        } else {
          return;
        }
      }

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete user');
      }

      setUsers(users.filter(u => u.id !== userId));
      toast({
        title: "Success",
        description: "User deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete user",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProject = async (projectId: number) => {
    try {
      setDeletingProjectId(projectId);
      const response = await fetch(`${API_URL}/admin/projects/${projectId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete project');
      }

      // Update local state
      setProjects(projects.filter(p => p.id !== projectId));

      // Invalidate the recipe books query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["recipeBooks"] });

      toast({
        title: "Success",
        description: "Project deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete project",
        variant: "destructive",
      });
    } finally {
      setDeletingProjectId(null);
    }
  };

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_URL}/admin/users`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      setUsers(data.users);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_URL}/admin/projects`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch projects');
      }

      const data = await response.json();
      setProjects(data.projects);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch projects",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInviteContributor = async (project: Project) => {
    setSelectedProject(project);
    setIsInviteDialogOpen(true);
  };

  const handleSendInvite = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedProject) return;

    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/admin/projects/${selectedProject.id}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          email: inviteEmail,
          deadline: inviteDeadline ? new Date(inviteDeadline).toISOString() : undefined,
          reminderFrequency: inviteReminderFrequency
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to invite contributor');
      }

      // Invalidate the query to force a refresh
      queryClient.invalidateQueries({ queryKey: ["recipeBooks", user?.role] });
      setIsInviteDialogOpen(false);
      setInviteEmail('');
      setInviteDeadline('');
      setInviteReminderFrequency('weekly');
      toast({
        title: "Success",
        description: "Invitation sent successfully",
      });
    } catch (error) {
      console.error('Error inviting contributor:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send invitation",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Support-related functions
  const fetchSupportStats = async () => {
    try {
      const response = await fetch(`${API_URL}/support/admin/stats`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSupportStats(data.stats);
      }
    } catch (error) {
      console.error('Error fetching support stats:', error);
    }
  };

  const testConnection = async () => {
    try {
      setIsTestingConnection(true);
      const response = await fetch(`${API_URL}/support/admin/test-connection`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setConnectionStatus(data.connection);

        toast({
          title: data.connection.connected ? "Connection Successful" : "Connection Failed",
          description: data.connection.connected
            ? `Successfully connected to ${data.connection.service}`
            : data.connection.error,
          variant: data.connection.connected ? "default" : "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test connection",
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const createTestTicket = async () => {
    try {
      setIsCreatingTestTicket(true);
      const response = await fetch(`${API_URL}/support/admin/test-ticket`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Test Ticket Created",
          description: `Test ticket created successfully. ID: ${data.ticket.id}`,
        });

        // Refresh stats and tickets
        fetchSupportStats();
        fetchSupportTickets();
      } else {
        const error = await response.json();
        throw new Error(error.details || 'Failed to create test ticket');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create test ticket",
        variant: "destructive",
      });
    } finally {
      setIsCreatingTestTicket(false);
    }
  };

  const fetchSupportTickets = async (page = 1, status = 'all') => {
    try {
      setIsLoadingTickets(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10'
      });

      if (status && status !== 'all') {
        params.append('status', status);
      }

      const response = await fetch(`${API_URL}/support/admin/tickets?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSupportTickets(data.tickets);
        setTicketsPagination(data.pagination);
      }
    } catch (error) {
      console.error('Error fetching support tickets:', error);
      toast({
        title: "Error",
        description: "Failed to fetch support tickets",
        variant: "destructive",
      });
    } finally {
      setIsLoadingTickets(false);
    }
  };

  const updateTicketStatus = async (ticketId: number, status: string) => {
    try {
      const response = await fetch(`${API_URL}/support/admin/tickets/${ticketId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ status })
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Ticket status updated successfully",
        });

        // Refresh tickets and stats
        fetchSupportTickets(ticketsPagination?.page || 1, ticketsFilter);
        fetchSupportStats();
      } else {
        const error = await response.json();
        throw new Error(error.details || 'Failed to update ticket status');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update ticket status",
        variant: "destructive",
      });
    }
  };

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'open': return 'destructive';
      case 'pending': return 'secondary';
      case 'solved': return 'default';
      case 'closed': return 'outline';
      default: return 'secondary';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'secondary';
      case 'normal': return 'outline';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const sendMessageToUser = async () => {
    if (!selectedTicketForContact || !contactMessage.trim()) return;

    try {
      setIsSendingMessage(true);
      const response = await fetch(`${API_URL}/support/admin/tickets/${selectedTicketForContact.id}/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          message: contactMessage.trim(),
          isInternal: false
        })
      });

      if (response.ok) {
        toast({
          title: "Message Sent",
          description: `Message sent to ${selectedTicketForContact.userName} successfully. They will receive an email notification.`,
        });

        setSelectedTicketForContact(null);
        setContactMessage('');
      } else {
        const error = await response.json();
        throw new Error(error.details || 'Failed to send message');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send message",
        variant: "destructive",
      });
    } finally {
      setIsSendingMessage(false);
    }
  };

  const cleanupClosedTickets = async () => {
    try {
      setIsDeletingTickets(true);
      const response = await fetch(`${API_URL}/support/admin/tickets/cleanup?olderThanDays=30`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Cleanup Complete",
          description: `Successfully deleted ${data.deletedCount} closed tickets older than 30 days.`,
        });

        // Refresh tickets and stats
        fetchSupportTickets(ticketsPagination?.page || 1, ticketsFilter);
        fetchSupportStats();
      } else {
        const error = await response.json();
        throw new Error(error.details || 'Failed to cleanup tickets');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cleanup tickets",
        variant: "destructive",
      });
    } finally {
      setIsDeletingTickets(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchProjects();
    fetchSupportStats();
    fetchSupportTickets();
  }, []);

  return (
    <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>

        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common administrative tasks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Link href="/admin/dashboard">
                  <Button variant="outline" className="flex items-center gap-2">
                    <FileCheck className="h-4 w-4" />
                    Review Recipe Submissions
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="users" className="space-y-4">
            <TabsList>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="projects">Projects</TabsTrigger>
              <TabsTrigger value="support">Support Tickets</TabsTrigger>
            </TabsList>

            <TabsContent value="users">
              <Card>
                <CardHeader>
                  <CardTitle>User Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h2 className="text-xl font-semibold">Users</h2>
                      <Button onClick={fetchUsers} disabled={isLoading}>
                        {isLoading ? "Loading..." : "Refresh Users"}
                      </Button>
                    </div>

                    <div className="space-y-4">
                      {users.map((user) => (
                        <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-muted-foreground">{user.email}</p>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Label htmlFor={`role-${user.id}`}>Role:</Label>
                              <select
                                id={`role-${user.id}`}
                                value={selectedRoles[user.id] || user.role}
                                onChange={(e) => {
                                  setSelectedRoles(prev => ({
                                    ...prev,
                                    [user.id]: e.target.value
                                  }));
                                  handleRoleChange(user.id, e.target.value);
                                }}
                                className="border rounded px-2 py-1"
                                disabled={isLoading}
                              >
                                <option value="contributor">Contributor</option>
                                <option value="organizer">Organizer</option>
                                <option value="admin">Admin</option>
                              </select>
                            </div>

                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="destructive">Delete</Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action cannot be undone. This will permanently delete the user
                                    and all associated data.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteUser(user.id)}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="projects">
              <Card>
                <CardHeader>
                  <CardTitle>Project Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h2 className="text-xl font-semibold">Projects</h2>
                      <Button onClick={fetchProjects} disabled={isLoading}>
                        {isLoading ? "Loading..." : "Refresh Projects"}
                      </Button>
                    </div>

                    <div className="grid gap-4">
                      {projects.map((project) => (
                        <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div>
                            <p className="font-medium">{project.name}</p>
                            <p className="text-sm text-muted-foreground">{project.description}</p>
                            <p className="text-sm text-muted-foreground">Status: {project.status}</p>
                            <p className="text-sm text-muted-foreground">
                              Organizer: {project.organizer.name} ({project.organizer.email})
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Contributors: {project.contributors.length}
                            </p>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              onClick={() => handleInviteContributor(project)}
                            >
                              Invite Contributor
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="destructive"
                                  disabled={deletingProjectId === project.id}
                                >
                                  {deletingProjectId === project.id ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Deleting...
                                    </>
                                  ) : (
                                    "Delete"
                                  )}
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action cannot be undone. This will permanently delete the project
                                    and all associated data.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteProject(project.id)}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                    disabled={deletingProjectId === project.id}
                                  >
                                    {deletingProjectId === project.id ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="support">
              <Card>
                <CardHeader>
                  <CardTitle>Support Ticket Management</CardTitle>
                  <CardDescription>
                    Manage customer support tickets and external service integration
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Support Service Status */}
                    <div className="p-4 border rounded-lg bg-muted/50">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold">External Service Integration</h3>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={testConnection}
                          disabled={isTestingConnection}
                          className="flex items-center gap-2"
                        >
                          {isTestingConnection ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <RefreshCw className="h-4 w-4" />
                          )}
                          Test Connection
                        </Button>
                      </div>
                      <div className="flex items-center gap-2">
                        {connectionStatus ? (
                          connectionStatus.connected ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )
                        ) : (
                          <div className="w-4 h-4 rounded-full bg-gray-400"></div>
                        )}
                        <span className="text-sm">
                          {connectionStatus ? (
                            connectionStatus.connected ? (
                              `Connected to ${connectionStatus.service}`
                            ) : (
                              `Failed to connect to ${connectionStatus.service}`
                            )
                          ) : (
                            import.meta.env.VITE_SUPPORT_SERVICE === 'zendesk' ? 'Zendesk (Not tested)' :
                            import.meta.env.VITE_SUPPORT_SERVICE === 'intercom' ? 'Intercom (Not tested)' :
                            'No external service configured'
                          )}
                        </span>
                      </div>
                      {connectionStatus && !connectionStatus.connected && connectionStatus.error && (
                        <p className="text-sm text-red-600 mt-2">
                          Error: {connectionStatus.error}
                        </p>
                      )}
                      <p className="text-sm text-muted-foreground mt-2">
                        Support tickets are automatically synchronized with the external service when configured.
                      </p>
                    </div>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="p-4 border rounded-lg text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {supportStats?.open || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Open</div>
                      </div>
                      <div className="p-4 border rounded-lg text-center">
                        <div className="text-2xl font-bold text-yellow-600">
                          {supportStats?.pending || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Pending</div>
                      </div>
                      <div className="p-4 border rounded-lg text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {supportStats?.solved || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Solved</div>
                      </div>
                      <div className="p-4 border rounded-lg text-center">
                        <div className="text-2xl font-bold text-gray-600">
                          {supportStats?.total || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Total</div>
                      </div>
                    </div>

                    {/* Ticket Filters and Actions */}
                    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                      <div className="flex gap-4 items-center">
                        <Select value={ticketsFilter} onValueChange={(value) => {
                          setTicketsFilter(value);
                          fetchSupportTickets(1, value);
                        }}>
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="Filter by status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Tickets</SelectItem>
                            <SelectItem value="open">Open</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="solved">Solved</SelectItem>
                          </SelectContent>
                        </Select>

                        <div className="text-sm text-muted-foreground">
                          Note: Closed tickets are automatically deleted from the system
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => fetchSupportTickets(ticketsPagination?.page || 1, ticketsFilter)}
                          disabled={isLoadingTickets}
                          className="flex items-center gap-2"
                        >
                          {isLoadingTickets ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <RefreshCw className="h-4 w-4" />
                          )}
                          Refresh
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={createTestTicket}
                          disabled={isCreatingTestTicket}
                          className="flex items-center gap-2"
                        >
                          {isCreatingTestTicket ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <TestTube className="h-4 w-4" />
                          )}
                          Create Test Ticket
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cleanupClosedTickets}
                          disabled={isDeletingTickets}
                          className="flex items-center gap-2"
                        >
                          {isDeletingTickets ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                          Cleanup Closed
                        </Button>
                      </div>
                    </div>

                    {/* Support Tickets List */}
                    <div className="space-y-4">
                      {isLoadingTickets ? (
                        <div className="p-8 text-center">
                          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                          <p className="text-muted-foreground">Loading tickets...</p>
                        </div>
                      ) : supportTickets.length === 0 ? (
                        <div className="p-8 text-center text-muted-foreground border rounded-lg bg-muted/20">
                          <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No support tickets found.</p>
                          <p className="text-sm">Create a test ticket to see how the system works.</p>
                        </div>
                      ) : (
                        <>
                          <div className="space-y-3">
                            {supportTickets.map((ticket) => (
                              <div key={ticket.id} className="p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center gap-2 mb-2">
                                      <span className="font-medium">#{ticket.id}</span>
                                      <Badge variant={getStatusBadgeVariant(ticket.status)}>
                                        {ticket.status}
                                      </Badge>
                                      <Badge variant={getPriorityBadgeVariant(ticket.priority)}>
                                        {ticket.priority}
                                      </Badge>
                                      {ticket.externalTicketId && (
                                        <Badge variant="outline" className="text-xs">
                                          Synced
                                        </Badge>
                                      )}
                                    </div>
                                    <h4 className="font-medium text-sm mb-1 truncate">{ticket.subject}</h4>
                                    <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                                      {ticket.description}
                                    </p>
                                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                      <div className="flex items-center gap-1">
                                        <User className="h-3 w-3" />
                                        {ticket.userName} ({ticket.userEmail})
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <Clock className="h-3 w-3" />
                                        {formatDate(ticket.createdAt)}
                                      </div>
                                      {ticket.category && (
                                        <Badge variant="outline" className="text-xs">
                                          {ticket.category}
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                  <div className="flex gap-2 ml-4">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => setSelectedTicketForContact(ticket)}
                                      className="flex items-center gap-1"
                                    >
                                      <Mail className="h-3 w-3" />
                                      Contact User
                                    </Button>
                                    <Select
                                      value={ticket.status}
                                      onValueChange={(status) => updateTicketStatus(ticket.id, status)}
                                    >
                                      <SelectTrigger className="w-32">
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="open">Open</SelectItem>
                                        <SelectItem value="pending">Pending</SelectItem>
                                        <SelectItem value="solved">Solved</SelectItem>
                                        <SelectItem value="closed">Closed</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>

                          {/* Pagination */}
                          {ticketsPagination && ticketsPagination.totalPages > 1 && (
                            <div className="flex items-center justify-between">
                              <p className="text-sm text-muted-foreground">
                                Showing {((ticketsPagination.page - 1) * ticketsPagination.limit) + 1} to{' '}
                                {Math.min(ticketsPagination.page * ticketsPagination.limit, ticketsPagination.total)} of{' '}
                                {ticketsPagination.total} tickets
                              </p>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => fetchSupportTickets(ticketsPagination.page - 1, ticketsFilter)}
                                  disabled={ticketsPagination.page <= 1 || isLoadingTickets}
                                >
                                  <ChevronLeft className="h-4 w-4" />
                                  Previous
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => fetchSupportTickets(ticketsPagination.page + 1, ticketsFilter)}
                                  disabled={ticketsPagination.page >= ticketsPagination.totalPages || isLoadingTickets}
                                >
                                  Next
                                  <ChevronRight className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>

                    {/* Integration Instructions */}
                    <div className="p-4 border-l-4 border-blue-500 bg-blue-50">
                      <h4 className="font-semibold text-blue-800">Setup Instructions</h4>
                      <div className="text-blue-700 text-sm mt-2 space-y-1">
                        <p>To enable external service integration:</p>
                        <ol className="list-decimal list-inside space-y-1 ml-4">
                          <li>Configure environment variables for Zendesk or Intercom</li>
                          <li>Set SUPPORT_SERVICE=zendesk or SUPPORT_SERVICE=intercom</li>
                          <li>Add your API credentials to the server environment</li>
                          <li>Restart the server to apply changes</li>
                        </ol>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Invite Contributor Dialog */}
          <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Invite Contributor</DialogTitle>
                <DialogDescription>
                  Invite a contributor to join this project.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSendInvite} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    placeholder="Enter contributor's email"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="deadline">Submission Deadline (Optional)</Label>
                  <Input
                    id="deadline"
                    type="datetime-local"
                    value={inviteDeadline}
                    onChange={(e) => setInviteDeadline(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reminderFrequency">Reminder Frequency</Label>
                  <Select value={inviteReminderFrequency} onValueChange={setInviteReminderFrequency}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select reminder frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5min">Every 5 minutes (Test)</SelectItem>
                      <SelectItem value="15min">Every 15 minutes (Test)</SelectItem>
                      <SelectItem value="30min">Every 30 minutes (Test)</SelectItem>
                      <SelectItem value="1hour">Every hour (Test)</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="biweekly">Bi-weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <DialogFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Sending..." : "Send Invitation"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>

          {/* Contact User Dialog */}
          <Dialog open={!!selectedTicketForContact} onOpenChange={() => setSelectedTicketForContact(null)}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Contact User</DialogTitle>
                <DialogDescription>
                  Send a message to {selectedTicketForContact?.userName} regarding ticket #{selectedTicketForContact?.id}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div className="p-3 bg-muted rounded-lg">
                  <p className="text-sm font-medium">Ticket: {selectedTicketForContact?.subject}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    User: {selectedTicketForContact?.userName} ({selectedTicketForContact?.userEmail})
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contact-message">Your Message</Label>
                  <Textarea
                    id="contact-message"
                    value={contactMessage}
                    onChange={(e) => setContactMessage(e.target.value)}
                    placeholder="Type your message to the user..."
                    rows={5}
                  />
                  <p className="text-xs text-muted-foreground">
                    The user will receive this message via email and can reply directly.
                  </p>
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setSelectedTicketForContact(null)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={sendMessageToUser}
                  disabled={!contactMessage.trim() || isSendingMessage}
                  className="flex items-center gap-2"
                >
                  {isSendingMessage ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                  {isSendingMessage ? 'Sending...' : 'Send Message'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </ProtectedRoute>
  );
}