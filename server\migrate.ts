import { fileURLToPath } from 'url';
import { dirname } from 'path';
import postgres from 'postgres';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

const createTablesQuery = `
-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  password TEXT NOT NULL,
  name TEXT NOT NULL,
  role VARCHAR(20) NOT NULL DEFAULT 'contributor',
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
  phone TEXT,
  address JSONB,
  preferences JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create projects table (Recipe Books)
CREATE TABLE IF NOT EXISTS projects (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  organizer_id INTEGER REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
  theme TEXT DEFAULT 'classic',
  font TEXT DEFAULT 'elegant',
  chapter_style TEXT DEFAULT 'simple',
  cover TEXT DEFAULT 'classic',
  cover_title TEXT,
  cover_subtitle TEXT,
  cover_image TEXT,
  use_custom_cover_image BOOLEAN DEFAULT false,
  dedication TEXT,
  include_dedication BOOLEAN DEFAULT true,
  include_quotes BOOLEAN DEFAULT true,
  family_quotes JSONB DEFAULT '[]'::jsonb,
  status TEXT DEFAULT 'draft',
  max_contributors INTEGER NOT NULL,
  deadline TIMESTAMP,
  pricing_tier TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  role TEXT DEFAULT 'organizer',
  "order" INTEGER
);

-- Create project_contributors table
CREATE TABLE IF NOT EXISTS project_contributors (
  id SERIAL PRIMARY KEY,
  project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE ON UPDATE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
  status TEXT DEFAULT 'pending',
  role TEXT DEFAULT 'contributor',
  invitation_token TEXT,
  invitation_sent_at TIMESTAMP,
  invitation_accepted_at TIMESTAMP,
  invitation_expires_at TIMESTAMP,
  joined_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deadline TIMESTAMP,
  last_reminder_sent_at TIMESTAMP,
  reminder_frequency TEXT,
  invited_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- Create recipes table
CREATE TABLE IF NOT EXISTS recipes (
  id SERIAL PRIMARY KEY,
  project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE ON UPDATE CASCADE,
  contributor_id INTEGER REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT,
  ingredients JSONB NOT NULL,
  measurement_system TEXT DEFAULT 'us',
  instructions JSONB NOT NULL,
  tags JSONB,
  images JSONB DEFAULT '[]'::jsonb,
  role TEXT DEFAULT 'contributor',
  status TEXT DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "order" INTEGER,
  is_ocr_generated BOOLEAN DEFAULT false,
  ocr_source_image TEXT,
  ocr_raw_text TEXT,
  ocr_extracted_data JSONB,
  is_voice_transcribed BOOLEAN DEFAULT false,
  voice_source_audio TEXT,
  voice_raw_text TEXT,
  voice_extracted_data JSONB,
  invited_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- Create recipe_comments table
CREATE TABLE IF NOT EXISTS recipe_comments (
  id SERIAL PRIMARY KEY,
  recipe_id INTEGER REFERENCES recipes(id) ON DELETE CASCADE ON UPDATE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
  comment TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
  type TEXT NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  metadata JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create print_orders table for Blurb API integration
CREATE TABLE IF NOT EXISTS print_orders (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE NOT NULL,
  blurb_order_id TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  book_data JSONB NOT NULL,
  shipping_address JSONB NOT NULL,
  tracking_url TEXT,
  shipping_info JSONB,
  estimated_delivery TIMESTAMP,
  total_cost TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
`;

const runMigration = async () => {
  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL is not set in environment variables');
  }

  const sql = postgres(process.env.DATABASE_URL, { max: 1 });

  try {
    console.log('Creating all tables...');
    await sql.unsafe(createTablesQuery);
    console.log('All tables created successfully');
  } catch (error) {
    console.error('Error creating tables:', error);
    process.exit(1);
  } finally {
    await sql.end();
  }
};

runMigration();