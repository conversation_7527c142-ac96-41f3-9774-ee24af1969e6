import React from 'react';
import { Button } from '@/components/ui/button';
import { MessageSquare } from 'lucide-react';
import { IntercomAPI } from './IntercomChat';

export function IntercomTestButton() {
  const handleShowIntercom = () => {
    console.log('Attempting to show Intercom...');
    console.log('Window.Intercom exists:', !!window.Intercom);
    
    if (window.Intercom) {
      IntercomAPI.show();
    } else {
      console.error('Intercom not loaded yet');
      alert('Intercom is not loaded yet. Please wait a moment and try again.');
    }
  };

  const handleShowNewMessage = () => {
    console.log('Attempting to show new message...');
    if (window.Intercom) {
      IntercomAPI.showNewMessage('Hello! I need help with RecipeBook.');
    } else {
      console.error('Intercom not loaded yet');
      alert('Intercom is not loaded yet. Please wait a moment and try again.');
    }
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 space-y-2">
      <Button
        onClick={handleShowIntercom}
        variant="outline"
        size="sm"
        className="bg-white shadow-lg"
      >
        <MessageSquare className="h-4 w-4 mr-2" />
        Show Intercom
      </Button>
      <Button
        onClick={handleShowNewMessage}
        variant="outline"
        size="sm"
        className="bg-white shadow-lg block w-full"
      >
        New Message
      </Button>
    </div>
  );
}
