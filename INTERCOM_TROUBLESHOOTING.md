# Intercom Loading Icon Issue - Troubleshooting Guide

## 🔍 Current Issue
The Intercom widget appears but shows only a loading icon when opened.

## 🛠 Troubleshooting Steps

### 1. Check App ID Format
Your current App ID: `ztbwnxvr`

**Verify in Intercom Dashboard:**
1. Go to Settings > Installation > Web
2. Look for "App ID" - it should match exactly
3. Common formats:
   - Short format: `ztbwnxvr` (8 characters)
   - Long format: `5d708733-6fde-4611-b3a4-325a51f18886` (UUID)

### 2. Debug Components Added
I've added several debug tools to help diagnose the issue:

**Debug Panel (Top-right):**
- Shows current App ID and configuration
- Displays user information
- Shows Intercom loading status
- Refresh button to update info

**Test Buttons (Bottom-right):**
- "Show Intercom" - Manually trigger widget
- "New Message" - Open with pre-filled message
- "Test Intercom Methods" - Test if Intercom API is responsive

### 3. Check Browser Console
Look for these messages:
```
✅ Good: "Intercom script loaded"
✅ Good: "Intercom successfully initialized"
❌ Bad: "Failed to load Intercom script"
❌ Bad: "Intercom failed to initialize properly"
```

### 4. Common Causes of Loading Icon

#### A. Wrong App ID
- **Solution**: Verify App ID in Intercom dashboard matches `.env` file
- **Check**: Debug panel shows correct App ID

#### B. Workspace Restrictions
- **Issue**: Intercom workspace might have domain restrictions
- **Solution**: In Intercom Settings > Security, check allowed domains
- **Add**: `localhost:5173` and your production domain

#### C. User Identification Issues
- **Issue**: Problems with user data format
- **Solution**: Try without user identification first
- **Check**: Debug panel shows user data format

#### D. Network/CORS Issues
- **Issue**: Script loading blocked by network/firewall
- **Check**: Network tab in browser dev tools
- **Look for**: Failed requests to `widget.intercom.io`

### 5. Testing Steps

#### Step 1: Basic Loading Test
1. Open browser dev tools (F12)
2. Go to Console tab
3. Refresh page
4. Look for Intercom initialization messages

#### Step 2: Manual Widget Test
1. Click "Show Intercom" button (bottom-right)
2. Check if widget opens properly
3. If still loading, check console for errors

#### Step 3: Reinitialize Test
1. Click "Reinitialize Intercom" in debug panel
2. Wait 2-3 seconds
3. Try opening widget again

#### Step 4: Network Test
1. Open Network tab in dev tools
2. Refresh page
3. Look for requests to `widget.intercom.io`
4. Check if any requests fail

### 6. Alternative Solutions

#### Option A: Try Without User Identification
```typescript
// Minimal Intercom setup
window.Intercom('boot', { 
  app_id: 'ztbwnxvr' 
});
```

#### Option B: Check Intercom Workspace Settings
1. Go to Intercom Settings > Messenger
2. Check if messenger is enabled
3. Verify no IP restrictions

#### Option C: Test with Different App ID
If you have access to another Intercom workspace, try with that App ID to isolate the issue.

### 7. Environment Variables Check

**Current Configuration:**
```bash
VITE_INTERCOM_APP_ID=ztbwnxvr
VITE_INTERCOM_ACCESS_TOKEN=************************************************************
```

**Verify:**
- App ID matches Intercom dashboard exactly
- No extra spaces or characters
- Environment variables are loaded (check debug panel)

### 8. Next Steps

1. **Check Debug Panel**: Look at the debug info in top-right
2. **Test Methods**: Use "Test Intercom Methods" button
3. **Console Logs**: Check browser console for errors
4. **Network Tab**: Verify script loading
5. **Intercom Dashboard**: Confirm workspace settings

### 9. If Still Not Working

**Provide this information:**
1. Browser console logs (especially errors)
2. Network tab showing failed requests
3. Debug panel information
4. Intercom workspace settings (domain restrictions, etc.)

The debug tools will help identify exactly where the issue is occurring!
