import { db } from '../db.js';
import { supportTickets, supportTicketMessages, users } from '../schema.js';
import { eq, desc, ne, and, sql } from 'drizzle-orm';

// Types for external service integration
export interface CreateTicketRequest {
  userId?: number;
  subject: string;
  description: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  category?: string;
  userEmail?: string;
  userName?: string;
}

export interface TicketResponse {
  id: number;
  externalTicketId?: string;
  subject: string;
  status: string;
  priority: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface MessageRequest {
  ticketId: number;
  authorId?: number;
  authorType: 'user' | 'admin' | 'system';
  message: string;
  isInternal?: boolean;
  attachments?: string[];
}

// Zendesk Integration
class ZendeskService {
  private subdomain: string;
  private email: string;
  private apiToken: string;
  private baseUrl: string;

  constructor() {
    this.subdomain = process.env.ZENDESK_SUBDOMAIN || '';
    this.email = process.env.ZENDESK_EMAIL || '';
    this.apiToken = process.env.ZENDESK_API_TOKEN || '';
    this.baseUrl = `https://${this.subdomain}.zendesk.com/api/v2`;
  }

  private getAuthHeader(): string {
    const credentials = Buffer.from(`${this.email}/token:${this.apiToken}`).toString('base64');
    return `Basic ${credentials}`;
  }

  async createTicket(request: CreateTicketRequest): Promise<string | null> {
    if (!this.subdomain || !this.email || !this.apiToken) {
      console.log('Zendesk credentials not configured, skipping external ticket creation');
      return null;
    }

    try {
      const ticketData = {
        ticket: {
          subject: request.subject,
          comment: {
            body: request.description
          },
          priority: request.priority || 'normal',
          type: 'question',
          requester: {
            email: request.userEmail || '<EMAIL>',
            name: request.userName || 'RecipeBook User'
          },
          tags: request.category ? [request.category] : []
        }
      };

      const response = await fetch(`${this.baseUrl}/tickets.json`, {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ticketData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Zendesk API error:', response.status, errorText);
        return null;
      }

      const result = await response.json();
      return result.ticket?.id?.toString() || null;
    } catch (error) {
      console.error('Error creating Zendesk ticket:', error);
      return null;
    }
  }

  async addComment(externalTicketId: string, message: string, isInternal: boolean = false): Promise<boolean> {
    if (!this.subdomain || !this.email || !this.apiToken) {
      return false;
    }

    try {
      const commentData = {
        ticket: {
          comment: {
            body: message,
            public: !isInternal
          }
        }
      };

      const response = await fetch(`${this.baseUrl}/tickets/${externalTicketId}.json`, {
        method: 'PUT',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(commentData)
      });

      return response.ok;
    } catch (error) {
      console.error('Error adding Zendesk comment:', error);
      return false;
    }
  }

  async updateTicketStatus(externalTicketId: string, status: string): Promise<boolean> {
    if (!this.subdomain || !this.email || !this.apiToken) {
      return false;
    }

    try {
      const updateData = {
        ticket: {
          status: status
        }
      };

      const response = await fetch(`${this.baseUrl}/tickets/${externalTicketId}.json`, {
        method: 'PUT',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      return response.ok;
    } catch (error) {
      console.error('Error updating Zendesk ticket status:', error);
      return false;
    }
  }
}

// Intercom Integration
class IntercomService {
  private accessToken: string;
  private baseUrl: string = 'https://api.intercom.io';

  constructor() {
    this.accessToken = process.env.INTERCOM_ACCESS_TOKEN || '';
  }

  private getAuthHeader(): string {
    return `Bearer ${this.accessToken}`;
  }

  async createTicket(request: CreateTicketRequest): Promise<string | null> {
    if (!this.accessToken) {
      console.log('Intercom credentials not configured, skipping external ticket creation');
      return null;
    }

    try {
      console.log('Creating Intercom conversation for:', request.userEmail);

      // First, find or create the user in Intercom
      let intercomUserId = await this.findOrCreateUser(request.userEmail!, request.userName!);

      if (!intercomUserId) {
        console.error('Failed to find or create Intercom user');
        return null;
      }

      // Create the conversation
      const conversationData = {
        from: {
          type: 'user',
          id: intercomUserId
        },
        body: `Subject: ${request.subject}\n\n${request.description}`,
        message_type: 'email'
      };

      const response = await fetch(`${this.baseUrl}/conversations`, {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Intercom-Version': '2.10'
        },
        body: JSON.stringify(conversationData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Intercom API error:', response.status, errorText);
        return null;
      }

      const result = await response.json();
      console.log('Intercom conversation created successfully:', result.id);
      return result.id || null;
    } catch (error) {
      console.error('Error creating Intercom conversation:', error);
      return null;
    }
  }

  private async findOrCreateUser(email: string, name: string): Promise<string | null> {
    try {
      // Try to find existing user
      const searchResponse = await fetch(`${this.baseUrl}/contacts/search`, {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Intercom-Version': '2.10'
        },
        body: JSON.stringify({
          query: {
            field: 'email',
            operator: '=',
            value: email
          }
        })
      });

      if (searchResponse.ok) {
        const searchResult = await searchResponse.json();
        if (searchResult.data && searchResult.data.length > 0) {
          console.log('Found existing Intercom user:', searchResult.data[0].id);
          return searchResult.data[0].id;
        }
      }

      // Create new user if not found
      const createResponse = await fetch(`${this.baseUrl}/contacts`, {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Intercom-Version': '2.10'
        },
        body: JSON.stringify({
          role: 'user',
          email: email,
          name: name,
          custom_attributes: {
            source: 'RecipeBook Support'
          }
        })
      });

      if (createResponse.ok) {
        const createResult = await createResponse.json();
        console.log('Created new Intercom user:', createResult.id);
        return createResult.id;
      } else {
        const errorText = await createResponse.text();
        console.error('Error creating Intercom user:', createResponse.status, errorText);
        return null;
      }
    } catch (error) {
      console.error('Error finding/creating Intercom user:', error);
      return null;
    }
  }

  async addReply(externalTicketId: string, message: string, isInternal: boolean = false): Promise<boolean> {
    if (!this.accessToken) {
      return false;
    }

    try {
      console.log('Adding reply to Intercom conversation:', externalTicketId);

      const replyData = {
        message_type: 'comment',
        type: 'admin',
        body: message,
        admin_id: process.env.INTERCOM_ADMIN_ID || undefined
      };

      const response = await fetch(`${this.baseUrl}/conversations/${externalTicketId}/reply`, {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Intercom-Version': '2.10'
        },
        body: JSON.stringify(replyData)
      });

      if (response.ok) {
        console.log('Successfully added reply to Intercom conversation');
        return true;
      } else {
        const errorText = await response.text();
        console.error('Error adding Intercom reply:', response.status, errorText);
        return false;
      }
    } catch (error) {
      console.error('Error adding Intercom reply:', error);
      return false;
    }
  }

  async updateConversationState(externalTicketId: string, state: 'open' | 'closed' | 'snoozed'): Promise<boolean> {
    if (!this.accessToken) {
      return false;
    }

    try {
      console.log('Updating Intercom conversation state:', externalTicketId, state);

      const response = await fetch(`${this.baseUrl}/conversations/${externalTicketId}`, {
        method: 'PUT',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Intercom-Version': '2.10'
        },
        body: JSON.stringify({
          state: state
        })
      });

      if (response.ok) {
        console.log('Successfully updated Intercom conversation state');
        return true;
      } else {
        const errorText = await response.text();
        console.error('Error updating Intercom conversation state:', response.status, errorText);
        return false;
      }
    } catch (error) {
      console.error('Error updating Intercom conversation state:', error);
      return false;
    }
  }

  async getConversation(externalTicketId: string): Promise<any | null> {
    if (!this.accessToken) {
      return null;
    }

    try {
      const response = await fetch(`${this.baseUrl}/conversations/${externalTicketId}`, {
        method: 'GET',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Accept': 'application/json',
          'Intercom-Version': '2.10'
        }
      });

      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        const errorText = await response.text();
        console.error('Error fetching Intercom conversation:', response.status, errorText);
        return null;
      }
    } catch (error) {
      console.error('Error fetching Intercom conversation:', error);
      return null;
    }
  }
}

// Main Support Service
export class SupportService {
  private zendeskService: ZendeskService;
  private intercomService: IntercomService;
  private activeService: 'zendesk' | 'intercom' | 'none';

  constructor() {
    this.zendeskService = new ZendeskService();
    this.intercomService = new IntercomService();
    this.activeService = (process.env.SUPPORT_SERVICE as 'zendesk' | 'intercom') || 'none';
  }

  async createTicket(request: CreateTicketRequest): Promise<TicketResponse> {
    console.log('Creating support ticket:', { subject: request.subject, service: this.activeService });

    // Create external ticket if service is configured
    let externalTicketId: string | null = null;

    if (this.activeService === 'zendesk') {
      externalTicketId = await this.zendeskService.createTicket(request);
    } else if (this.activeService === 'intercom') {
      externalTicketId = await this.intercomService.createTicket(request);
    }

    // Create internal ticket record
    const [ticket] = await db.insert(supportTickets).values({
      userId: request.userId || null,
      externalTicketId,
      subject: request.subject,
      description: request.description,
      priority: request.priority || 'normal',
      category: request.category || null,
      status: 'open',
      userEmail: request.userEmail || null,
      userName: request.userName || null,
      tags: [],
      metadata: {
        service: this.activeService
      }
    }).returning();

    console.log('Support ticket created successfully:', { id: ticket.id, externalId: externalTicketId });

    return {
      id: ticket.id,
      externalTicketId: ticket.externalTicketId || undefined,
      subject: ticket.subject,
      status: ticket.status,
      priority: ticket.priority || 'normal',
      createdAt: ticket.createdAt || new Date(),
      updatedAt: ticket.updatedAt || new Date()
    };
  }

  async addMessage(request: MessageRequest): Promise<void> {
    console.log('Adding message to ticket:', { ticketId: request.ticketId, authorType: request.authorType });

    // Get ticket details for external service integration
    const [ticket] = await db.select().from(supportTickets).where(eq(supportTickets.id, request.ticketId));

    if (!ticket) {
      throw new Error('Ticket not found');
    }

    // Add message to external service if configured
    if (ticket.externalTicketId) {
      if (this.activeService === 'zendesk') {
        await this.zendeskService.addComment(ticket.externalTicketId, request.message, request.isInternal || false);
      } else if (this.activeService === 'intercom') {
        await this.intercomService.addReply(ticket.externalTicketId, request.message, request.isInternal || false);
      }
    }

    // Add message to internal database
    await db.insert(supportTicketMessages).values({
      ticketId: request.ticketId,
      authorId: request.authorId || null,
      authorType: request.authorType,
      message: request.message,
      isInternal: request.isInternal || false,
      attachments: request.attachments || []
    });

    // If this is an admin message to a user, send email notification
    if (request.authorType === 'admin' && !request.isInternal) {
      await this.sendEmailNotification(ticket, request.message);
    }

    console.log('Message added successfully to ticket:', request.ticketId);
  }

  private async sendEmailNotification(ticket: any, message: string): Promise<void> {
    try {
      // Ensure we have the complete ticket data with user information
      if (!ticket.userEmail || !ticket.userName) {
        console.log('Incomplete ticket data for email notification, fetching from database...');

        // Join with users table to get complete user information
        const [fullTicketData] = await db.select({
          id: supportTickets.id,
          subject: supportTickets.subject,
          description: supportTickets.description,
          status: supportTickets.status,
          userEmail: supportTickets.userEmail,
          userName: supportTickets.userName,
          userId: supportTickets.userId,
          // Also get user data from users table as fallback
          userTableEmail: users.email,
          userTableName: users.name
        })
        .from(supportTickets)
        .leftJoin(users, eq(supportTickets.userId, users.id))
        .where(eq(supportTickets.id, ticket.id));

        if (fullTicketData) {
          // Use ticket data first, fallback to users table data
          ticket = {
            ...ticket,
            ...fullTicketData,
            userEmail: fullTicketData.userEmail || fullTicketData.userTableEmail,
            userName: fullTicketData.userName || fullTicketData.userTableName
          };
        }
      }

      // In a real implementation, you would integrate with an email service like SendGrid, AWS SES, etc.
      console.log('Sending email notification to:', ticket.userEmail);
      console.log('Subject: Update on your support ticket #' + ticket.id);
      console.log('Message:', message);

      // For now, we'll just log the email that would be sent
      // In production, you would implement actual email sending here
      const emailContent = `
        Dear ${ticket.userName || 'Valued Customer'},

        You have received an update on your support ticket #${ticket.id}:

        Subject: ${ticket.subject || 'Support Request'}

        Message from our support team:
        ${message}

        You can reply to this email or contact us through our support system.

        Best regards,
        RecipeBook Support Team
      `;

      console.log('Email content:', emailContent);
    } catch (error) {
      console.error('Error sending email notification:', error);
      // Don't throw error as this is not critical for the main functionality
    }
  }

  async updateTicketStatus(ticketId: number, status: string, assignedToId?: number): Promise<void> {
    console.log('Updating ticket status:', { ticketId, status, assignedToId });

    // Get ticket details
    const [ticket] = await db.select().from(supportTickets).where(eq(supportTickets.id, ticketId));

    if (!ticket) {
      throw new Error('Ticket not found');
    }

    // Update external service if configured
    if (ticket.externalTicketId) {
      if (this.activeService === 'zendesk') {
        await this.zendeskService.updateTicketStatus(ticket.externalTicketId, status);
      } else if (this.activeService === 'intercom') {
        // Map internal status to Intercom conversation states
        let intercomState: 'open' | 'closed' | 'snoozed' = 'open';
        if (status === 'closed' || status === 'solved') {
          intercomState = 'closed';
        } else if (status === 'pending') {
          intercomState = 'snoozed';
        }
        await this.intercomService.updateConversationState(ticket.externalTicketId, intercomState);
      }
    }

    // Update internal database
    const updateData: any = {
      status,
      updatedAt: new Date()
    };

    if (assignedToId !== undefined) {
      updateData.assignedToId = assignedToId;
    }

    if (status === 'solved' || status === 'closed') {
      updateData.resolvedAt = new Date();
    }

    await db.update(supportTickets)
      .set(updateData)
      .where(eq(supportTickets.id, ticketId));

    console.log('Ticket status updated successfully:', ticketId);
  }

  async getTickets(userId?: number, status?: string, assignedToId?: number, includeClosedTickets = false): Promise<TicketResponse[]> {
    let query = db.select().from(supportTickets);

    // Add filters
    const conditions = [];
    if (userId) conditions.push(eq(supportTickets.userId, userId));
    if (status) {
      conditions.push(eq(supportTickets.status, status));
    } else if (!includeClosedTickets) {
      // By default, exclude closed tickets unless specifically requested
      conditions.push(ne(supportTickets.status, 'closed'));
    }
    if (assignedToId) conditions.push(eq(supportTickets.assignedToId, assignedToId));

    if (conditions.length > 0) {
      // Use and() to combine all conditions properly
      query = query.where(and(...conditions));
    }

    const tickets = await query.orderBy(desc(supportTickets.createdAt));

    return tickets.map(ticket => ({
      id: ticket.id,
      externalTicketId: ticket.externalTicketId || undefined,
      subject: ticket.subject,
      description: ticket.description,
      status: ticket.status,
      priority: ticket.priority || 'normal',
      category: ticket.category,
      userEmail: ticket.userEmail,
      userName: ticket.userName,
      assignedToId: ticket.assignedToId,
      createdAt: ticket.createdAt || new Date(),
      updatedAt: ticket.updatedAt || new Date(),
      resolvedAt: ticket.resolvedAt
    }));
  }

  async getTicketWithMessages(ticketId: number): Promise<any> {
    // Get ticket details
    const [ticket] = await db.select().from(supportTickets).where(eq(supportTickets.id, ticketId));

    if (!ticket) {
      throw new Error('Ticket not found');
    }

    // Get messages
    const messages = await db.select().from(supportTicketMessages)
      .where(eq(supportTicketMessages.ticketId, ticketId))
      .orderBy(supportTicketMessages.createdAt);

    return {
      ...ticket,
      messages
    };
  }

  async assignTicket(ticketId: number, assignedToId: number): Promise<void> {
    await this.updateTicketStatus(ticketId, 'pending', assignedToId);
  }

  async getTicketStats(): Promise<any> {
    // This would typically involve more complex queries
    // For now, return basic stats
    const allTickets = await db.select().from(supportTickets);

    const stats = {
      total: allTickets.length,
      open: allTickets.filter(t => t.status === 'open').length,
      pending: allTickets.filter(t => t.status === 'pending').length,
      solved: allTickets.filter(t => t.status === 'solved').length,
      closed: allTickets.filter(t => t.status === 'closed').length
    };

    return stats;
  }

  async syncExternalTicket(ticketId: number): Promise<void> {
    console.log('Syncing external ticket data for ticket:', ticketId);

    const [ticket] = await db.select().from(supportTickets).where(eq(supportTickets.id, ticketId));

    if (!ticket || !ticket.externalTicketId) {
      console.log('No external ticket to sync for ticket:', ticketId);
      return;
    }

    try {
      if (this.activeService === 'intercom') {
        const conversation = await this.intercomService.getConversation(ticket.externalTicketId);

        if (conversation) {
          // Update ticket status based on Intercom conversation state
          let internalStatus = ticket.status;
          if (conversation.state === 'closed') {
            internalStatus = 'closed';
          } else if (conversation.state === 'snoozed') {
            internalStatus = 'pending';
          } else if (conversation.state === 'open') {
            internalStatus = 'open';
          }

          // Update metadata with external conversation data
          const metadata = {
            ...ticket.metadata as any,
            lastSyncAt: new Date().toISOString(),
            externalState: conversation.state,
            externalUpdatedAt: conversation.updated_at,
            conversationParts: conversation.conversation_parts?.conversation_parts?.length || 0
          };

          await db.update(supportTickets)
            .set({
              status: internalStatus,
              metadata,
              updatedAt: new Date()
            })
            .where(eq(supportTickets.id, ticketId));

          console.log('Successfully synced external ticket data');
        }
      }
    } catch (error) {
      console.error('Error syncing external ticket:', error);
    }
  }

  async testConnection(): Promise<{ service: string; connected: boolean; error?: string }> {
    console.log('Testing connection to external support service:', this.activeService);

    if (this.activeService === 'none') {
      return { service: 'none', connected: false, error: 'No external service configured' };
    }

    try {
      if (this.activeService === 'intercom') {
        // Test Intercom connection by trying to search for a non-existent user
        const testResponse = await fetch(`${this.intercomService['baseUrl']}/contacts/search`, {
          method: 'POST',
          headers: {
            'Authorization': this.intercomService['getAuthHeader'](),
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Intercom-Version': '2.10'
          },
          body: JSON.stringify({
            query: {
              field: 'email',
              operator: '=',
              value: '<EMAIL>'
            }
          })
        });

        if (testResponse.ok || testResponse.status === 404) {
          return { service: 'intercom', connected: true };
        } else {
          const errorText = await testResponse.text();
          return { service: 'intercom', connected: false, error: `HTTP ${testResponse.status}: ${errorText}` };
        }
      } else if (this.activeService === 'zendesk') {
        // Test Zendesk connection - this would need to be implemented
        return { service: 'zendesk', connected: false, error: 'Zendesk connection test not implemented' };
      }

      return { service: this.activeService, connected: false, error: 'Unknown service' };
    } catch (error) {
      return {
        service: this.activeService,
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async deleteTicket(ticketId: number): Promise<void> {
    console.log('Deleting ticket:', ticketId);

    // First check if ticket exists
    const [ticket] = await db.select().from(supportTickets).where(eq(supportTickets.id, ticketId));

    if (!ticket) {
      throw new Error('Ticket not found');
    }

    // Delete associated messages first (foreign key constraint)
    await db.delete(supportTicketMessages).where(eq(supportTicketMessages.ticketId, ticketId));

    // Delete the ticket
    await db.delete(supportTickets).where(eq(supportTickets.id, ticketId));

    console.log('Ticket deleted successfully:', ticketId);
  }

  async deleteClosedTickets(olderThanDays: number): Promise<number> {
    console.log(`Deleting closed tickets older than ${olderThanDays} days`);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    // Get tickets to delete
    const ticketsToDelete = await db.select({ id: supportTickets.id })
      .from(supportTickets)
      .where(
        and(
          eq(supportTickets.status, 'closed'),
          // Use resolvedAt if available, otherwise use updatedAt
          // This ensures we only delete tickets that have been closed for the specified time
          sql`COALESCE(${supportTickets.resolvedAt}, ${supportTickets.updatedAt}) < ${cutoffDate.toISOString()}`
        )
      );

    if (ticketsToDelete.length === 0) {
      console.log('No closed tickets found to delete');
      return 0;
    }

    const ticketIds = ticketsToDelete.map(t => t.id);
    console.log(`Found ${ticketIds.length} closed tickets to delete:`, ticketIds);

    // Delete associated messages first (foreign key constraint)
    for (const ticketId of ticketIds) {
      await db.delete(supportTicketMessages).where(eq(supportTicketMessages.ticketId, ticketId));
    }

    // Delete the tickets
    const deleteResult = await db.delete(supportTickets)
      .where(
        and(
          eq(supportTickets.status, 'closed'),
          sql`COALESCE(${supportTickets.resolvedAt}, ${supportTickets.updatedAt}) < ${cutoffDate.toISOString()}`
        )
      );

    console.log(`Successfully deleted ${ticketIds.length} closed tickets`);
    return ticketIds.length;
  }
}

// Export singleton instance
export const supportService = new SupportService();
