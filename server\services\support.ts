import { db } from '../db.js';
import { supportTickets, supportTicketMessages, users } from '../schema.js';
import { eq, desc } from 'drizzle-orm';

// Types for external service integration
export interface CreateTicketRequest {
  userId?: number;
  subject: string;
  description: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  category?: string;
  userEmail?: string;
  userName?: string;
}

export interface TicketResponse {
  id: number;
  externalTicketId?: string;
  subject: string;
  status: string;
  priority: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface MessageRequest {
  ticketId: number;
  authorId?: number;
  authorType: 'user' | 'admin' | 'system';
  message: string;
  isInternal?: boolean;
  attachments?: string[];
}

// Zendesk Integration
class ZendeskService {
  private subdomain: string;
  private email: string;
  private apiToken: string;
  private baseUrl: string;

  constructor() {
    this.subdomain = process.env.ZENDESK_SUBDOMAIN || '';
    this.email = process.env.ZENDESK_EMAIL || '';
    this.apiToken = process.env.ZENDESK_API_TOKEN || '';
    this.baseUrl = `https://${this.subdomain}.zendesk.com/api/v2`;
  }

  private getAuthHeader(): string {
    const credentials = Buffer.from(`${this.email}/token:${this.apiToken}`).toString('base64');
    return `Basic ${credentials}`;
  }

  async createTicket(request: CreateTicketRequest): Promise<string | null> {
    if (!this.subdomain || !this.email || !this.apiToken) {
      console.log('Zendesk credentials not configured, skipping external ticket creation');
      return null;
    }

    try {
      const ticketData = {
        ticket: {
          subject: request.subject,
          comment: {
            body: request.description
          },
          priority: request.priority || 'normal',
          type: 'question',
          requester: {
            email: request.userEmail || '<EMAIL>',
            name: request.userName || 'RecipeBook User'
          },
          tags: request.category ? [request.category] : []
        }
      };

      const response = await fetch(`${this.baseUrl}/tickets.json`, {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ticketData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Zendesk API error:', response.status, errorText);
        return null;
      }

      const result = await response.json();
      return result.ticket?.id?.toString() || null;
    } catch (error) {
      console.error('Error creating Zendesk ticket:', error);
      return null;
    }
  }

  async addComment(externalTicketId: string, message: string, isInternal: boolean = false): Promise<boolean> {
    if (!this.subdomain || !this.email || !this.apiToken) {
      return false;
    }

    try {
      const commentData = {
        ticket: {
          comment: {
            body: message,
            public: !isInternal
          }
        }
      };

      const response = await fetch(`${this.baseUrl}/tickets/${externalTicketId}.json`, {
        method: 'PUT',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(commentData)
      });

      return response.ok;
    } catch (error) {
      console.error('Error adding Zendesk comment:', error);
      return false;
    }
  }

  async updateTicketStatus(externalTicketId: string, status: string): Promise<boolean> {
    if (!this.subdomain || !this.email || !this.apiToken) {
      return false;
    }

    try {
      const updateData = {
        ticket: {
          status: status
        }
      };

      const response = await fetch(`${this.baseUrl}/tickets/${externalTicketId}.json`, {
        method: 'PUT',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      return response.ok;
    } catch (error) {
      console.error('Error updating Zendesk ticket status:', error);
      return false;
    }
  }
}

// Intercom Integration
class IntercomService {
  private accessToken: string;
  private baseUrl: string = 'https://api.intercom.io';

  constructor() {
    this.accessToken = process.env.INTERCOM_ACCESS_TOKEN || '';
  }

  private getAuthHeader(): string {
    return `Bearer ${this.accessToken}`;
  }

  async createTicket(request: CreateTicketRequest): Promise<string | null> {
    if (!this.accessToken) {
      console.log('Intercom credentials not configured, skipping external ticket creation');
      return null;
    }

    try {
      const ticketData = {
        message_type: 'email',
        subject: request.subject,
        body: request.description,
        from: {
          type: 'user',
          email: request.userEmail || '<EMAIL>'
        }
      };

      const response = await fetch(`${this.baseUrl}/conversations`, {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(ticketData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Intercom API error:', response.status, errorText);
        return null;
      }

      const result = await response.json();
      return result.id || null;
    } catch (error) {
      console.error('Error creating Intercom conversation:', error);
      return null;
    }
  }

  async addReply(externalTicketId: string, message: string): Promise<boolean> {
    if (!this.accessToken) {
      return false;
    }

    try {
      const replyData = {
        message_type: 'comment',
        type: 'admin',
        body: message
      };

      const response = await fetch(`${this.baseUrl}/conversations/${externalTicketId}/reply`, {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(replyData)
      });

      return response.ok;
    } catch (error) {
      console.error('Error adding Intercom reply:', error);
      return false;
    }
  }
}

// Main Support Service
export class SupportService {
  private zendeskService: ZendeskService;
  private intercomService: IntercomService;
  private activeService: 'zendesk' | 'intercom' | 'none';

  constructor() {
    this.zendeskService = new ZendeskService();
    this.intercomService = new IntercomService();
    this.activeService = (process.env.SUPPORT_SERVICE as 'zendesk' | 'intercom') || 'none';
  }

  async createTicket(request: CreateTicketRequest): Promise<TicketResponse> {
    console.log('Creating support ticket:', { subject: request.subject, service: this.activeService });

    // Create external ticket if service is configured
    let externalTicketId: string | null = null;

    if (this.activeService === 'zendesk') {
      externalTicketId = await this.zendeskService.createTicket(request);
    } else if (this.activeService === 'intercom') {
      externalTicketId = await this.intercomService.createTicket(request);
    }

    // Create internal ticket record
    const [ticket] = await db.insert(supportTickets).values({
      userId: request.userId || null,
      externalTicketId,
      subject: request.subject,
      description: request.description,
      priority: request.priority || 'normal',
      category: request.category || null,
      status: 'open',
      tags: [],
      metadata: {
        service: this.activeService,
        userEmail: request.userEmail,
        userName: request.userName
      }
    }).returning();

    console.log('Support ticket created successfully:', { id: ticket.id, externalId: externalTicketId });

    return {
      id: ticket.id,
      externalTicketId: ticket.externalTicketId || undefined,
      subject: ticket.subject,
      status: ticket.status,
      priority: ticket.priority || 'normal',
      createdAt: ticket.createdAt || new Date(),
      updatedAt: ticket.updatedAt || new Date()
    };
  }

  async addMessage(request: MessageRequest): Promise<void> {
    console.log('Adding message to ticket:', { ticketId: request.ticketId, authorType: request.authorType });

    // Get ticket details for external service integration
    const [ticket] = await db.select().from(supportTickets).where(eq(supportTickets.id, request.ticketId));

    if (!ticket) {
      throw new Error('Ticket not found');
    }

    // Add message to external service if configured
    if (ticket.externalTicketId) {
      if (this.activeService === 'zendesk') {
        await this.zendeskService.addComment(ticket.externalTicketId, request.message, request.isInternal || false);
      } else if (this.activeService === 'intercom') {
        await this.intercomService.addReply(ticket.externalTicketId, request.message);
      }
    }

    // Add message to internal database
    await db.insert(supportTicketMessages).values({
      ticketId: request.ticketId,
      authorId: request.authorId || null,
      authorType: request.authorType,
      message: request.message,
      isInternal: request.isInternal || false,
      attachments: request.attachments || []
    });

    console.log('Message added successfully to ticket:', request.ticketId);
  }

  async updateTicketStatus(ticketId: number, status: string, assignedToId?: number): Promise<void> {
    console.log('Updating ticket status:', { ticketId, status, assignedToId });

    // Get ticket details
    const [ticket] = await db.select().from(supportTickets).where(eq(supportTickets.id, ticketId));

    if (!ticket) {
      throw new Error('Ticket not found');
    }

    // Update external service if configured
    if (ticket.externalTicketId) {
      if (this.activeService === 'zendesk') {
        await this.zendeskService.updateTicketStatus(ticket.externalTicketId, status);
      }
      // Note: Intercom doesn't have direct status updates, it uses conversation states
    }

    // Update internal database
    const updateData: any = {
      status,
      updatedAt: new Date()
    };

    if (assignedToId !== undefined) {
      updateData.assignedToId = assignedToId;
    }

    if (status === 'solved' || status === 'closed') {
      updateData.resolvedAt = new Date();
    }

    await db.update(supportTickets)
      .set(updateData)
      .where(eq(supportTickets.id, ticketId));

    console.log('Ticket status updated successfully:', ticketId);
  }

  async getTickets(userId?: number, status?: string, assignedToId?: number): Promise<TicketResponse[]> {
    let query = db.select().from(supportTickets);

    // Add filters
    const conditions = [];
    if (userId) conditions.push(eq(supportTickets.userId, userId));
    if (status) conditions.push(eq(supportTickets.status, status));
    if (assignedToId) conditions.push(eq(supportTickets.assignedToId, assignedToId));

    if (conditions.length > 0) {
      // For simplicity, we'll just use the first condition
      // In a real implementation, you'd use `and()` to combine conditions
      query = query.where(conditions[0]);
    }

    const tickets = await query.orderBy(desc(supportTickets.createdAt));

    return tickets.map(ticket => ({
      id: ticket.id,
      externalTicketId: ticket.externalTicketId || undefined,
      subject: ticket.subject,
      status: ticket.status,
      priority: ticket.priority || 'normal',
      createdAt: ticket.createdAt || new Date(),
      updatedAt: ticket.updatedAt || new Date()
    }));
  }

  async getTicketWithMessages(ticketId: number): Promise<any> {
    // Get ticket details
    const [ticket] = await db.select().from(supportTickets).where(eq(supportTickets.id, ticketId));

    if (!ticket) {
      throw new Error('Ticket not found');
    }

    // Get messages
    const messages = await db.select().from(supportTicketMessages)
      .where(eq(supportTicketMessages.ticketId, ticketId))
      .orderBy(supportTicketMessages.createdAt);

    return {
      ...ticket,
      messages
    };
  }

  async assignTicket(ticketId: number, assignedToId: number): Promise<void> {
    await this.updateTicketStatus(ticketId, 'pending', assignedToId);
  }

  async getTicketStats(): Promise<any> {
    // This would typically involve more complex queries
    // For now, return basic stats
    const allTickets = await db.select().from(supportTickets);

    const stats = {
      total: allTickets.length,
      open: allTickets.filter(t => t.status === 'open').length,
      pending: allTickets.filter(t => t.status === 'pending').length,
      solved: allTickets.filter(t => t.status === 'solved').length,
      closed: allTickets.filter(t => t.status === 'closed').length
    };

    return stats;
  }
}

// Export singleton instance
export const supportService = new SupportService();
