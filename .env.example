# Database
DATABASE_URL=postgresql://username:password@localhost:5432/recipebook

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-here

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=recipe-book-images-bucket

# Email Configuration (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# Blurb API Configuration
BLURB_API_KEY=your-blurb-api-key
BLURB_API_URL=https://api.rpiprint.com
BLURB_ENVIRONMENT=production

# Intercom Configuration
INTERCOM_ACCESS_TOKEN=your-intercom-access-token
INTERCOM_APP_ID=your-intercom-app-id
INTERCOM_WEBHOOK_SECRET=your-intercom-webhook-secret

# Client Environment Variables (for client/.env)
VITE_API_URL=http://localhost:5000/api
VITE_INTERCOM_APP_ID=your-intercom-app-id
VITE_INTERCOM_ACCESS_TOKEN=your-intercom-access-token
